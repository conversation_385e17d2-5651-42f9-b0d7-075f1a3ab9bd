name: <PERSON><PERSON>er Check

on:
  pull_request:
    branches: [ main, master, dev*, core/*, desktop/* ]

jobs:
  prettier:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'

    - name: Install dependencies
      run: npm ci

    - name: Run Prettier check
      run: npm run format:check
