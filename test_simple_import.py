#!/usr/bin/env python3
"""
Simple test script to check imports
Run this from the main qsComfy directory: python test_simple_import.py
"""

import sys
import os

# Add the current directory to Python path (qsComfy root)
sys.path.insert(0, os.getcwd())

print("Testing basic imports...")
print(f"Current working directory: {os.getcwd()}")
print(f"Python path includes: {sys.path[0]}")

try:
    print("1. Testing artlet_models...")
    from artlets.artlet_models import ExecutionStatus, ClientStatus
    print("   ✓ artlet_models OK")
except Exception as e:
    print(f"   ✗ artlet_models failed: {e}")
    import traceback
    traceback.print_exc()

try:
    print("2. Testing artlet_exceptions...")
    from artlets.artlet_exceptions import DeadlockException
    print("   ✓ artlet_exceptions OK")
except Exception as e:
    print(f"   ✗ artlet_exceptions failed: {e}")

try:
    print("3. Testing basic data structures...")
    from artlets.artlet_models import BlockData, ExecutionInfo
    block = BlockData(block_id="test")
    print(f"   ✓ BlockData created: {block.block_id}")
    print(f"   ✓ ExecutionInfo status: {block.execution_info.execution_status}")
except Exception as e:
    print(f"   ✗ Data structures failed: {e}")

try:
    print("4. Testing helper functions...")
    from artlets.artlet_models import update_execution_status, get_heartbeat_age
    import time
    current_time = time.time()
    age = get_heartbeat_age(current_time - 10)
    print(f"   ✓ Heartbeat age calculation: {age:.2f}s")
except Exception as e:
    print(f"   ✗ Helper functions failed: {e}")

print("\nBasic functionality test complete!")
print("If all tests passed, the core implementation is working.")
