Use the Vue 3 Composition API instead of the Options API when writing Vue components. An exception is when overriding or extending a PrimeVue component for compatibility, you may use the Options API.

Use setup() function for component logic

Utilize ref and reactive for reactive state

Implement computed properties with computed()

Use watch and watchEffect for side effects

Implement lifecycle hooks with onMounted, onUpdated, etc.

Utilize provide/inject for dependency injection

Use vue 3.5 style of default prop declaration.

Use Tailwind CSS for styling

Leverage VueUse functions for performance-enhancing styles

Use lodash for utility functions

Use TypeScript for type safety

Implement proper props and emits definitions

Utilize Vue 3's Teleport component when needed

Use Suspense for async components

Implement proper error handling

Follow Vue 3 style guide and naming conventions

Use Vite for fast development and building

Use vue-i18n in composition API for any string literals. Place new translation entries in src/locales/en/main.json.