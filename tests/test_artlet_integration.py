"""
Integration test script for artlet deadlock detection
This script tests the actual HTTP endpoints and smart waiting functionality
"""

import requests
import time
import json
import sys
import os

# Add qsComfy root directory to path
qscomfy_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, qscomfy_root)

def test_server_endpoints(server_url="http://localhost:8188"):
    """Test the new artlet endpoints on a running server"""
    
    print(f"Testing artlet endpoints on {server_url}")
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f"{server_url}/system_stats", timeout=5)
        if response.status_code == 200:
            print("✓ Server is running and accessible")
        else:
            print(f"✗ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to server: {e}")
        return False
    
    # Test 2: Test execution status endpoint (should return 404 for non-existent prompt)
    try:
        response = requests.get(f"{server_url}/artlet/execution_status/test_prompt/test_block", timeout=5)
        if response.status_code == 404:
            print("✓ Execution status endpoint is accessible (404 expected for non-existent prompt)")
        else:
            print(f"✓ Execution status endpoint responded with status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Execution status endpoint error: {e}")
        return False
    
    # Test 3: Test client status endpoint
    try:
        response = requests.get(f"{server_url}/artlet/client_status/test_client/test_prompt", timeout=5)
        if response.status_code == 404:
            print("✓ Client status endpoint is accessible (404 expected for non-existent client)")
        else:
            print(f"✓ Client status endpoint responded with status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Client status endpoint error: {e}")
        return False
    
    # Test 4: Test prompt status endpoint
    try:
        response = requests.get(f"{server_url}/artlet/prompt_status/test_prompt", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "active" in data:
                print(f"✓ Prompt status endpoint working: {data}")
            else:
                print(f"✗ Prompt status endpoint missing 'active' field: {data}")
        else:
            print(f"✗ Prompt status endpoint error: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Prompt status endpoint error: {e}")
        return False
    
    # Test 5: Test client heartbeat endpoint
    try:
        response = requests.post(f"{server_url}/artlet/client_heartbeat/test_client", timeout=5)
        if response.status_code == 404:
            print("✓ Client heartbeat endpoint is accessible (404 expected for non-existent client)")
        else:
            print(f"✓ Client heartbeat endpoint responded with status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Client heartbeat endpoint error: {e}")
        return False
    
    print("\n✓ All endpoint tests completed successfully!")
    return True

def simulate_client_server_interaction():
    """Simulate a client-server interaction to test smart waiting"""
    
    print("\n" + "="*50)
    print("SIMULATING CLIENT-SERVER INTERACTION")
    print("="*50)
    
    # This would require actual artlet blocks to be set up
    # For now, we'll just test the data structures
    
    from artlets.artlet_models import (
        ExecutionStatus, ClientStatus, ExecutionInfo, ClientInfo, 
        BlockData, ClientData, PromptData
    )
    
    # Create test data structures
    print("Creating test data structures...")
    
    # Server side
    server_block = BlockData(block_id="server_block_1")
    server_prompt = PromptData(prompt_id="test_prompt")
    server_prompt.blocks["server_block_1"] = server_block
    
    # Client side  
    client_data = ClientData(client_id="test_client")
    client_data.prompts["test_prompt"] = server_prompt
    
    print("✓ Data structures created")
    
    # Test execution status updates
    print("Testing execution status updates...")
    
    from artlets.artlet_models import update_execution_status, update_client_status
    
    # Server starts waiting for input
    update_execution_status(
        server_block, 
        ExecutionStatus.WAITING_INPUT,
        waiting_for=["input_0", "input_1"],
        waiting_for_block="server_block_1"
    )
    
    print(f"✓ Server status: {server_block.execution_info.execution_status}")
    print(f"✓ Server waiting for: {server_block.execution_info.waiting_for}")
    
    # Client sends data
    update_client_status(
        client_data,
        ClientStatus.SENDING_DATA,
        current_operation="sending_inputs_server_block_1"
    )
    
    print(f"✓ Client status: {client_data.client_info.client_status}")
    print(f"✓ Client operation: {client_data.client_info.current_operation}")
    
    # Server starts executing
    update_execution_status(
        server_block,
        ExecutionStatus.EXECUTING,
        current_node="some_processing_node"
    )
    
    print(f"✓ Server now executing: {server_block.execution_info.current_node}")
    
    # Client waits for response
    update_client_status(
        client_data,
        ClientStatus.WAITING_RESPONSE,
        current_operation="waiting_for_output_server_block_1"
    )
    
    print(f"✓ Client waiting for response")
    
    # Test heartbeat age
    from artlets.artlet_models import get_heartbeat_age, is_heartbeat_stale
    
    heartbeat_age = get_heartbeat_age(server_block.execution_info.last_heartbeat)
    is_stale = is_heartbeat_stale(server_block.execution_info.last_heartbeat, max_age_seconds=60)
    
    print(f"✓ Heartbeat age: {heartbeat_age:.2f} seconds")
    print(f"✓ Heartbeat stale: {is_stale}")
    
    print("\n✓ Client-server interaction simulation completed!")

def test_exception_handling():
    """Test the custom exception classes"""
    
    print("\n" + "="*50)
    print("TESTING EXCEPTION HANDLING")
    print("="*50)
    
    from artlets.artlet_exceptions import (
        DeadlockException, ClientDisconnectedException, 
        ServerUnavailableException, TimeoutException
    )
    
    # Test exception creation and inheritance
    exceptions_to_test = [
        (DeadlockException, "Test deadlock detected"),
        (ClientDisconnectedException, "Client disconnected"),
        (ServerUnavailableException, "Server unavailable"),
        (TimeoutException, "Operation timed out")
    ]
    
    for exc_class, message in exceptions_to_test:
        try:
            raise exc_class(message)
        except exc_class as e:
            print(f"✓ {exc_class.__name__}: {e}")
        except Exception as e:
            print(f"✗ Unexpected exception type: {type(e).__name__}: {e}")
    
    print("\n✓ Exception handling tests completed!")

def main():
    """Run all tests"""
    
    print("ARTLET DEADLOCK DETECTION - INTEGRATION TESTS")
    print("=" * 60)
    
    # Test 1: Server endpoints (requires running server)
    server_running = test_server_endpoints()
    
    # Test 2: Data structure simulation (always runs)
    simulate_client_server_interaction()
    
    # Test 3: Exception handling (always runs)
    test_exception_handling()
    
    print("\n" + "=" * 60)
    if server_running:
        print("✓ ALL TESTS COMPLETED - Server endpoints working")
    else:
        print("⚠ TESTS COMPLETED - Server endpoints not tested (server not running)")
    print("✓ Data structures and exception handling working")
    print("=" * 60)

if __name__ == "__main__":
    main()
