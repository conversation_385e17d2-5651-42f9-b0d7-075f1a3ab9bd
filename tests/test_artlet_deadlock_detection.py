"""
Test suite for artlet deadlock detection and smart waiting functionality
"""

import unittest
import time
import json
from unittest.mock import Mock, patch, MagicMock

# Import the modules we want to test
import sys
import os

# Add qsComfy root directory to path
qscomfy_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, qscomfy_root)

from artlets.artlet_models import (
    ExecutionStatus, ClientStatus, ExecutionInfo, ClientInfo, BlockData,
    update_execution_status, update_client_status, get_pending_inputs,
    is_heartbeat_stale, get_heartbeat_age
)
from artlets.artlet_exceptions import (
    DeadlockException, ClientDisconnectedException, ServerUnavailableException,
    TimeoutException, ExecutionException, HeartbeatException
)

class TestExecutionStatusModels(unittest.TestCase):
    """Test the new execution status data models"""
    
    def setUp(self):
        self.block_data = BlockData(block_id="test_block")
        
    def test_execution_info_initialization(self):
        """Test ExecutionInfo is properly initialized"""
        exec_info = self.block_data.execution_info
        self.assertEqual(exec_info.execution_status, ExecutionStatus.IDLE)
        self.assertIsNone(exec_info.current_node)
        self.assertEqual(exec_info.waiting_for, [])
        self.assertIsInstance(exec_info.last_heartbeat, float)
        
    def test_update_execution_status(self):
        """Test updating execution status"""
        update_execution_status(
            self.block_data, 
            ExecutionStatus.EXECUTING,
            current_node="node_123",
            waiting_for=["input_1", "input_2"]
        )
        
        exec_info = self.block_data.execution_info
        self.assertEqual(exec_info.execution_status, ExecutionStatus.EXECUTING)
        self.assertEqual(exec_info.current_node, "node_123")
        self.assertEqual(exec_info.waiting_for, ["input_1", "input_2"])
        
    def test_heartbeat_age_calculation(self):
        """Test heartbeat age calculation"""
        old_time = time.time() - 60  # 1 minute ago
        age = get_heartbeat_age(old_time)
        self.assertGreater(age, 59)  # Should be around 60 seconds
        self.assertLess(age, 61)
        
    def test_heartbeat_stale_detection(self):
        """Test stale heartbeat detection"""
        old_time = time.time() - 150  # 2.5 minutes ago
        recent_time = time.time() - 30  # 30 seconds ago
        
        self.assertTrue(is_heartbeat_stale(old_time, max_age_seconds=120))
        self.assertFalse(is_heartbeat_stale(recent_time, max_age_seconds=120))

class TestSmartWaitingLogic(unittest.TestCase):
    """Test smart waiting logic components"""
    
    def setUp(self):
        # Mock the artlet client block for testing
        self.mock_client = Mock()
        self.mock_client.check_execution_status = Mock()
        self.mock_client.fetch_outputs = Mock()
        self.mock_client.is_waiting_for_our_input = Mock()
        self.mock_client.detect_input_deadlock = Mock()
        self.mock_client.confirm_deadlock = Mock()
        self.mock_client.get_heartbeat_age_from_status = Mock()
        
    def test_execution_status_response_parsing(self):
        """Test parsing of execution status responses"""
        mock_response = {
            "prompt_id": "test_prompt",
            "block_id": "test_block",
            "status": "executing",
            "current_node": "node_123",
            "waiting_for": [],
            "last_heartbeat": time.time(),
            "execution_progress": 0.5
        }
        
        # Test that we can parse the response correctly
        self.assertEqual(mock_response["status"], "executing")
        self.assertEqual(mock_response["current_node"], "node_123")
        self.assertEqual(mock_response["waiting_for"], [])
        
    def test_deadlock_detection_logic(self):
        """Test deadlock detection scenarios"""
        # Scenario 1: Server waiting for input we already sent
        exec_status = {
            "status": "waiting_input",
            "waiting_for": ["input_0", "input_1"],
            "waiting_for_block": "test_block"
        }
        
        self.mock_client.is_waiting_for_our_input.return_value = True
        self.mock_client.detect_input_deadlock.return_value = True
        
        # This should trigger deadlock detection
        self.assertTrue(self.mock_client.is_waiting_for_our_input(exec_status, "test_block"))
        self.assertTrue(self.mock_client.detect_input_deadlock(exec_status))

class TestEndpointIntegration(unittest.TestCase):
    """Test the new HTTP endpoints"""
    
    @patch('artlets.artlet_helpers.get_client_id_from_prompt_id')
    def test_execution_status_endpoint_structure(self, mock_get_client_id):
        """Test the structure of execution status endpoint response"""
        # This tests the expected response structure
        expected_response = {
            "prompt_id": "test_prompt",
            "block_id": "test_block", 
            "status": "executing",
            "current_node": "node_123",
            "waiting_for": ["input_1"],
            "waiting_for_block": "block_456",
            "last_heartbeat": time.time(),
            "execution_progress": 0.75,
            "estimated_remaining": 120,
            "error_message": None,
            "start_time": time.time() - 300
        }
        
        # Verify all required fields are present
        required_fields = [
            "prompt_id", "block_id", "status", "current_node", 
            "waiting_for", "last_heartbeat"
        ]
        
        for field in required_fields:
            self.assertIn(field, expected_response)

if __name__ == '__main__':
    unittest.main()
