name: Tests CI

on:
  push:
    branches: [main, master, core/*, desktop/*]
  pull_request:
    branches: [main, master, dev*, core/*, desktop/*]

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: Checkout ComfyUI
        uses: actions/checkout@v4
        with:
          repository: 'comfyanonymous/ComfyUI'
          path: 'ComfyUI'
          ref: master

      - name: Checkout ComfyUI_frontend
        uses: actions/checkout@v4
        with:
          repository: 'Comfy-Org/ComfyUI_frontend'
          path: 'ComfyUI_frontend'

      - name: Checkout ComfyUI_devtools
        uses: actions/checkout@v4
        with:
          repository: 'Comfy-Org/ComfyUI_devtools'
          path: 'ComfyUI/custom_nodes/ComfyUI_devtools'
          ref: 'd05fd48dd787a4192e16802d4244cfcc0e2f9684'

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Build ComfyUI_frontend
        run: |
          npm ci
          npm run build
        working-directory: ComfyUI_frontend

      - name: Generate cache key
        id: cache-key
        run: echo "key=$(date +%s)" >> $GITHUB_OUTPUT

      - name: Cache setup
        uses: actions/cache@v3
        with:
          path: |
            ComfyUI
            ComfyUI_frontend
          key: comfyui-setup-${{ steps.cache-key.outputs.key }}

  playwright-tests:
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, chromium-2x, mobile-chrome]
    steps:
      - name: Restore cached setup
        uses: actions/cache@v3
        with:
          path: |
            ComfyUI
            ComfyUI_frontend
          key: comfyui-setup-${{ needs.setup.outputs.cache-key }}

      - uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install requirements
        run: |
          python -m pip install --upgrade pip
          pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
          pip install -r requirements.txt
          pip install wait-for-it
        working-directory: ComfyUI

      - name: Start ComfyUI server
        run: |
          python main.py --cpu --multi-user --front-end-root ../ComfyUI_frontend/dist &
          wait-for-it --service 127.0.0.1:8188 -t 600
        working-directory: ComfyUI

      - name: Install Playwright Browsers
        run: npx playwright install chromium --with-deps
        working-directory: ComfyUI_frontend

      - name: Run Playwright tests (${{ matrix.browser }})
        run: npx playwright test --project=${{ matrix.browser }}
        working-directory: ComfyUI_frontend

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report-${{ matrix.browser }}
          path: ComfyUI_frontend/playwright-report/
          retention-days: 30
