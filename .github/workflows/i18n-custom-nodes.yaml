name: Update Locales for given custom node repository

on:
  workflow_dispatch:
    inputs:
      owner:
        description: 'Owner of the repository to update locales for'
        required: true
        type: string
      repository:
        description: 'Repository to update locales for'
        required: true
        type: string
      fork_owner:
        description: 'Owner of the forked repository'
        required: false
        type: string
        default: 'Comfy-Org'

jobs:
  update-locales:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout ComfyUI
      uses: actions/checkout@v4
      with:
        repository: comfyanonymous/ComfyUI
        path: ComfyUI
        ref: master
    - name: Checkout ComfyUI_frontend
      uses: actions/checkout@v4
      with:
        repository: Comfy-Org/ComfyUI_frontend
        path: ComfyUI_frontend
    - name: Checkout ComfyUI_devtools
      uses: actions/checkout@v4
      with:
        repository: Comfy-Org/ComfyUI_devtools
        path: ComfyUI/custom_nodes/ComfyUI_devtools
    - name: Checkout custom node repository
      uses: actions/checkout@v4
      with:
        repository: ${{ inputs.owner }}/${{ inputs.repository }}
        path: 'ComfyUI/custom_nodes/${{ inputs.repository }}'
    - uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
    - uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Install ComfyUI requirements
      run: |
        python -m pip install --upgrade pip
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
        pip install -r requirements.txt
        pip install wait-for-it
      working-directory: ComfyUI
    - name: Install custom node requirements
      run: |
        if [ -f "requirements.txt" ]; then
          pip install -r requirements.txt
        fi
      working-directory: ComfyUI/custom_nodes/${{ inputs.repository }}
    - name: Build & Install ComfyUI_frontend
      run: |
        npm ci
        npm run build
        rm -rf ../ComfyUI/web/*
        mv dist/* ../ComfyUI/web/
      working-directory: ComfyUI_frontend
    - name: Start ComfyUI server
      run: |
        python main.py --cpu --multi-user &
        wait-for-it --service 127.0.0.1:8188 -t 600
      working-directory: ComfyUI
    - name: Install Playwright Browsers
      run: npx playwright install chromium --with-deps
      working-directory: ComfyUI_frontend
    - name: Start dev server
      # Run electron dev server as it is a superset of the web dev server
      # We do want electron specific UIs to be translated.
      run: npm run dev:electron &
      working-directory: ComfyUI_frontend
    - name: Capture base i18n
      run: npx tsx scripts/diff-i18n capture
      working-directory: ComfyUI_frontend
    - name: Update en.json
      run: npm run collect-i18n
      env:
        PLAYWRIGHT_TEST_URL: http://localhost:5173
      working-directory: ComfyUI_frontend
    - name: Update translations
      run: npm run locale
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      working-directory: ComfyUI_frontend
    - name: Diff base vs updated i18n
      run: npx tsx scripts/diff-i18n diff
      working-directory: ComfyUI_frontend
    - name: Update i18n in custom node repository
      run: |
        LOCALE_DIR=ComfyUI/custom_nodes/${{ inputs.repository }}/locales/
        install -d "$LOCALE_DIR"
        cp -rf ComfyUI_frontend/temp/diff/* "$LOCALE_DIR"
    - name: Check and create fork of custom node repository
      run: |
        # Try to fork the repository
        gh repo fork ${{ inputs.owner }}/${{ inputs.repository }} --clone=false || {
          echo "Fork failed - repository might already be forked"
          # Exit 0 to prevent the workflow from failing
          exit 0
        }

        # Enable workflows on the forked repository
        gh api \
          --method PUT \
          -H "Accept: application/vnd.github+json" \
          "/repos/${{ inputs.fork_owner }}/${{ inputs.repository }}/actions/permissions/workflow" \
          -F can_approve_pull_request_reviews=true \
          -F default_workflow_permissions="write" \
          -F enabled=true
      env:
        GH_TOKEN: ${{ secrets.PR_GH_TOKEN }}

    - name: Commit changes
      working-directory: ComfyUI/custom_nodes/${{ inputs.repository }}
      run: |
        git config --global user.name 'github-actions'
        git config --global user.email '<EMAIL>'

        # Create and switch to new branch
        git checkout -b update-locales

        # Stage and commit changes
        git add -A
        git commit -m "Update locales"

    - name: Install SSH key For PUSH
      uses: shimataro/ssh-key-action@v2
      with:
        # PR private key from action server
        key: ${{ secrets.PR_SSH_PRIVATE_KEY }}
        # github public key to confirm it's github server
        known_hosts: github.com ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEAq2A7hRGmdnm9tUDbO9IDSwBK6TbQa+PXYPCPy6rbTrTtw7PHkccKrpp0yVhp5HdEIcKr6pLlVDBfOLX9QUsyCOV0wzfjIJNlGEYsdlLJizHhbn2mUjvSAHQqZETYP81eFzLQNnPHt4EVVUh7VfDESU84KezmD5QlWpXLmvU31/yMf+Se8xhHTvKSCZIFImWwoG6mbUoWf9nzpIoaSjB+weqqUUmpaaasXVal72J+UX2B+2RPW3RcT0eOzQgqlJL3RKrTJvdsjE3JEAvGq3lGHSZXy28G3skua2SmVi/w4yCE6gbODqnTWlg7+wC604ydGXA8VJiS5ap43JXiUFFAaQ==

    - name: Push changes
      working-directory: ComfyUI/custom_nodes/${{ inputs.repository }}
      run: |
        # Force push to create the branch
        echo "Pushing changes to ${{ inputs.fork_owner }}/${{ inputs.repository }}"
        git push -f **************:${{ inputs.fork_owner }}/${{ inputs.repository }}.git update-locales

    - name: Create PR
      working-directory: ComfyUI/custom_nodes/${{ inputs.repository }}
      run: |
        # Create PR using gh cli
        gh pr create --title "Update locales for ${{ inputs.repository }}" --repo ${{ inputs.owner }}/${{ inputs.repository }} --head ${{ inputs.fork_owner }}:update-locales --body "Update locales for ${{ inputs.repository }}"
      env:
        GH_TOKEN: ${{ secrets.PR_GH_TOKEN }}
