# Setting test expectation screenshots for Playwright
name: Update Playwright Expectations

on:
  pull_request:
    types: [ labeled ]

jobs:
  test:
    runs-on: ubuntu-latest
    if: github.event.label.name == 'New Browser Test Expectations'
    steps:
    - uses: Comfy-Org/ComfyUI_frontend_setup_action@v2.3
    - name: Install Playwright Browsers
      run: npx playwright install chromium --with-deps
      working-directory: ComfyUI_frontend
    - name: Run Playwright tests and update snapshots
      id: playwright-tests
      run: npx playwright test --update-snapshots
      continue-on-error: true
      working-directory: ComfyUI_frontend
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: ComfyUI_frontend/playwright-report/
        retention-days: 30
    - name: Debugging info
      run: |
        echo "Branch: ${{ github.head_ref }}"
        git status
      working-directory: ComfyUI_frontend
    - name: Commit updated expectations
      run: |
        git config --global user.name 'github-actions'
        git config --global user.email '<EMAIL>'
        git fetch origin ${{ github.head_ref }}
        git checkout -B ${{ github.head_ref }} origin/${{ github.head_ref }}
        git add browser_tests
        git commit -m "Update test expectations [skip ci]"
        git push origin HEAD:${{ github.head_ref }}
      working-directory: ComfyUI_frontend
