{"last_node_id": 0, "last_link_id": 18, "nodes": [{"id": "CheckpointLoaderSimple.0", "type": "CheckpointLoaderSimple", "pos": {"0": 100, "1": 130}, "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [12], "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [10, 11], "shape": 3}, {"name": "VAE", "type": "VAE", "links": [17], "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["v1-5-pruned-emaonly.ckpt"]}, {"id": "CLIPTextEncode.0", "type": "CLIPTextEncode", "pos": {"0": 515, "1": 130}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [13], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"]}, {"id": "CLIPTextEncode.1", "type": "CLIPTextEncode", "pos": {"0": 515, "1": 460}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 11}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [14], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": "EmptyLatentImage.0", "type": "EmptyLatentImage", "pos": {"0": 100, "1": 358}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [15], "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": "KSampler.0", "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 1015, "1": 130}, "size": {"0": 315, "1": 262}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 12}, {"name": "positive", "type": "CONDITIONING", "link": 13}, {"name": "negative", "type": "CONDITIONING", "link": 14}, {"name": "latent_image", "type": "LATENT", "link": 15}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [16], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [3, "randomize", 20, 8, "euler", "normal", 1]}, {"id": "VAEDecode.0", "type": "VAEDecode", "pos": {"0": 1430, "1": 130}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 16}, {"name": "vae", "type": "VAE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [18], "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": "SaveImage.0", "type": "SaveImage", "pos": {"0": 1740, "1": 130}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 18}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}], "links": [[10, "CheckpointLoaderSimple.0", 1, "CLIPTextEncode.0", 0, "CLIP"], [11, "CheckpointLoaderSimple.0", 1, "CLIPTextEncode.1", 0, "CLIP"], [12, "CheckpointLoaderSimple.0", 0, "KSampler.0", 0, "MODEL"], [13, "CLIPTextEncode.0", 0, "KSampler.0", 1, "CONDITIONING"], [14, "CLIPTextEncode.1", 0, "KSampler.0", 2, "CONDITIONING"], [15, "EmptyLatentImage.0", 0, "KSampler.0", 3, "LATENT"], [16, "KSampler.0", 0, "VAEDecode.0", 0, "LATENT"], [17, "CheckpointLoaderSimple.0", 2, "VAEDecode.0", 1, "VAE"], [18, "VAEDecode.0", 0, "SaveImage.0", 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [0, 0]}}, "version": 0.4}