@layer primevue, tailwind-utilities;

@layer tailwind-utilities {
  @tailwind components;
  @tailwind utilities;
}

:root {
  --fg-color: #000;
  --bg-color: #fff;
  --comfy-menu-bg: #353535;
  --comfy-menu-secondary-bg: #292929;
  --comfy-topbar-height: 2.5rem;
  --comfy-input-bg: #222;
  --input-text: #ddd;
  --descrip-text: #999;
  --drag-text: #ccc;
  --error-text: #ff4444;
  --border-color: #4e4e4e;
  --tr-even-bg-color: #222;
  --tr-odd-bg-color: #353535;
  --primary-bg: #236692;
  --primary-fg: #ffffff;
  --primary-hover-bg: #3485bb;
  --primary-hover-fg: #ffffff;
  --content-bg: #e0e0e0;
  --content-fg: #000;
  --content-hover-bg: #adadad;
  --content-hover-fg: #000;
}

@media (prefers-color-scheme: dark) {
  :root {
    --fg-color: #fff;
    --bg-color: #202020;
    --content-bg: #4e4e4e;
    --content-fg: #fff;
    --content-hover-bg: #222;
    --content-hover-fg: #fff;
  }
}

body {
  width: 100vw;
  height: 100vh;
  margin: 0;
  overflow: hidden;
  background: var(--bg-color) var(--bg-img);
  color: var(--fg-color);
  min-height: -webkit-fill-available;
  max-height: -webkit-fill-available;
  min-width: -webkit-fill-available;
  max-width: -webkit-fill-available;
  font-family: Arial, sans-serif;
}

.comfy-multiline-input {
  background-color: var(--comfy-input-bg);
  color: var(--input-text);
  overflow: hidden;
  overflow-y: auto;
  padding: 2px;
  resize: none;
  border: none;
  box-sizing: border-box;
  font-size: var(--comfy-textarea-font-size);
}

.comfy-markdown {
  /* We assign the textarea and the Tiptap editor to the same CSS grid area to stack them on top of one another. */
  display: grid;
}

.comfy-markdown > textarea {
  grid-area: 1 / 1 / 2 / 2;
}

.comfy-markdown .tiptap {
  grid-area: 1 / 1 / 2 / 2;
  background-color: var(--comfy-input-bg);
  color: var(--input-text);
  overflow: hidden;
  overflow-y: auto;
  resize: none;
  border: none;
  box-sizing: border-box;
  font-size: var(--comfy-textarea-font-size);
  height: 100%;
  padding: 0.5em;
}

.comfy-markdown.editing .tiptap {
  display: none;
}

.comfy-markdown .tiptap :first-child {
  margin-top: 0;
}

.comfy-markdown .tiptap :last-child {
  margin-bottom: 0;
}

.comfy-markdown .tiptap blockquote {
  border-left: medium solid;
  margin-left: 1em;
  padding-left: 0.5em;
}

.comfy-markdown .tiptap pre {
  border: thin dotted;
  border-radius: 0.5em;
  margin: 0.5em;
  padding: 0.5em;
}

.comfy-markdown .tiptap table {
  border-collapse: collapse;
}

.comfy-markdown .tiptap th {
  text-align: left;
  background: var(--comfy-menu-bg);
}

.comfy-markdown .tiptap th,
.comfy-markdown .tiptap td {
  padding: 0.5em;
  border: thin solid;
}

.comfy-modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 100; /* Sit on top */
  padding: 30px 30px 10px 30px;
  background-color: var(--comfy-menu-bg); /* Modal background */
  color: var(--error-text);
  box-shadow: 0 0 20px #888888;
  border-radius: 10px;
  top: 50%;
  left: 50%;
  max-width: 80vw;
  max-height: 80vh;
  transform: translate(-50%, -50%);
  overflow: hidden;
  justify-content: center;
  font-family: monospace;
  font-size: 15px;
}

.comfy-modal-content {
  display: flex;
  flex-direction: column;
}

.comfy-modal p {
  overflow: auto;
  white-space: pre-line; /* This will respect line breaks */
  margin-bottom: 20px; /* Add some margin between the text and the close button*/
}

.comfy-modal select,
.comfy-modal input[type='button'],
.comfy-modal input[type='checkbox'] {
  margin: 3px 3px 3px 4px;
}

.comfy-menu {
  font-size: 15px;
  position: absolute;
  top: 50%;
  right: 0;
  text-align: center;
  z-index: 999;
  width: 190px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--descrip-text);
  background-color: var(--comfy-menu-bg);
  font-family: sans-serif;
  padding: 10px;
  border-radius: 0 8px 8px 8px;
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
}

.comfy-menu-header {
  display: flex;
}

.comfy-menu-actions {
  display: flex;
  gap: 3px;
  align-items: center;
  height: 20px;
  position: relative;
  top: -1px;
  font-size: 22px;
}

.comfy-menu .comfy-menu-actions button {
  background-color: rgba(0, 0, 0, 0);
  padding: 0;
  border: none;
  cursor: pointer;
  font-size: inherit;
}

.comfy-menu .comfy-menu-actions .comfy-settings-btn {
  font-size: 0.6em;
}

button.comfy-close-menu-btn {
  font-size: 1em;
  line-height: 12px;
  color: #ccc;
  position: relative;
  top: -1px;
}

.comfy-menu-queue-size {
  flex: auto;
}

.comfy-menu button,
.comfy-modal button {
  font-size: 20px;
}

.comfy-menu-btns {
  margin-bottom: 10px;
  width: 100%;
}

.comfy-menu-btns button {
  font-size: 10px;
  width: 50%;
  color: var(--descrip-text) !important;
}

.comfy-menu > button {
  width: 100%;
}

.comfy-btn,
.comfy-menu > button,
.comfy-menu-btns button,
.comfy-menu .comfy-list button,
.comfy-modal button {
  color: var(--input-text);
  background-color: var(--comfy-input-bg);
  border-radius: 8px;
  border-color: var(--border-color);
  border-style: solid;
  margin-top: 2px;
}

.comfy-btn:hover:not(:disabled),
.comfy-menu > button:hover,
.comfy-menu-btns button:hover,
.comfy-menu .comfy-list button:hover,
.comfy-modal button:hover,
.comfy-menu-actions button:hover {
  filter: brightness(1.2);
  will-change: transform;
  cursor: pointer;
}

span.drag-handle {
  width: 10px;
  height: 20px;
  display: inline-block;
  overflow: hidden;
  line-height: 5px;
  padding: 3px 4px;
  cursor: move;
  vertical-align: middle;
  margin-top: -0.4em;
  margin-left: -0.2em;
  font-size: 12px;
  font-family: sans-serif;
  letter-spacing: 2px;
  color: var(--drag-text);
  text-shadow: 1px 0 1px black;
  touch-action: none;
}

span.drag-handle::after {
  content: '.. .. ..';
}

.comfy-queue-btn {
  width: 100%;
}

.comfy-list {
  color: var(--descrip-text);
  background-color: var(--comfy-menu-bg);
  margin-bottom: 10px;
  border-color: var(--border-color);
  border-style: solid;
}

.comfy-list-items {
  overflow-y: scroll;
  max-height: 100px;
  min-height: 25px;
  background-color: var(--comfy-input-bg);
  padding: 5px;
}

.comfy-list h4 {
  min-width: 160px;
  margin: 0;
  padding: 3px;
  font-weight: normal;
}

.comfy-list-items button {
  font-size: 10px;
}

.comfy-list-actions {
  margin: 5px;
  display: flex;
  gap: 5px;
  justify-content: center;
}

.comfy-list-actions button {
  font-size: 12px;
}

button.comfy-queue-btn {
  margin: 6px 0 !important;
}

.comfy-modal.comfy-settings,
.comfy-modal.comfy-manage-templates {
  text-align: center;
  font-family: sans-serif;
  color: var(--descrip-text);
  z-index: 99;
}

.comfy-modal.comfy-settings input[type='range'] {
  vertical-align: middle;
}

.comfy-modal.comfy-settings input[type='range'] + input[type='number'] {
  width: 3.5em;
}

.comfy-modal input,
.comfy-modal select {
  color: var(--input-text);
  background-color: var(--comfy-input-bg);
  border-radius: 8px;
  border-color: var(--border-color);
  border-style: solid;
  font-size: inherit;
}

.comfy-tooltip-indicator {
  text-decoration: underline;
  text-decoration-style: dashed;
}

@media only screen and (max-height: 850px) {
  .comfy-menu {
    top: 0 !important;
    bottom: 0 !important;
    left: auto !important;
    right: 0 !important;
    border-radius: 0;
  }

  .comfy-menu span.drag-handle {
    display: none;
  }

  .comfy-menu-queue-size {
    flex: unset;
  }

  .comfy-menu-header {
    justify-content: space-between;
  }
  .comfy-menu-actions {
    gap: 10px;
    font-size: 28px;
  }
}

/* Input popup */

.graphdialog {
  min-height: 1em;
  background-color: var(--comfy-menu-bg);
  z-index: 41; /* z-index is set to 41 here in order to appear over selection-overlay-container which should have a z-index of 40 */
}

.graphdialog .name {
  font-size: 14px;
  font-family: sans-serif;
  color: var(--descrip-text);
}

.graphdialog button {
  margin-top: unset;
  vertical-align: unset;
  height: 1.6em;
  padding-right: 8px;
}

.graphdialog input,
.graphdialog textarea,
.graphdialog select {
  background-color: var(--comfy-input-bg);
  border: 2px solid;
  border-color: var(--border-color);
  color: var(--input-text);
  border-radius: 12px 0 0 12px;
}

/* Dialogs */

dialog {
  box-shadow: 0 0 20px #888888;
}

dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
}

.comfy-dialog.comfyui-dialog.comfy-modal {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform: none;
}

.comfy-dialog.comfy-modal {
  font-family: Arial, sans-serif;
  border-color: var(--bg-color);
  box-shadow: none;
  border: 2px solid var(--border-color);
}

.comfy-dialog .comfy-modal-content {
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;
  color: var(--fg-color);
}

.comfy-dialog .comfy-modal-content h3 {
  margin-top: 0;
}

.comfy-dialog .comfy-modal-content > p {
  width: 100%;
}

.comfy-dialog .comfy-modal-content > .comfyui-button {
  flex: 1;
  justify-content: center;
}

/* Context menu */

.litegraph .dialog {
  z-index: 1;
  font-family: Arial, sans-serif;
}

.litegraph .litemenu-entry.has_submenu {
  position: relative;
  padding-right: 20px;
}

.litemenu-entry.has_submenu::after {
  content: '>';
  position: absolute;
  top: 0;
  right: 2px;
}

.litegraph.litecontextmenu,
.litegraph.litecontextmenu.dark {
  z-index: 9999 !important;
  background-color: var(--comfy-menu-bg) !important;
}

.litegraph.litecontextmenu
  .litemenu-entry:hover:not(.disabled):not(.separator) {
  background-color: var(--comfy-menu-hover-bg, var(--border-color)) !important;
  color: var(--fg-color);
}

.litegraph.litecontextmenu .litemenu-entry.submenu,
.litegraph.litecontextmenu.dark .litemenu-entry.submenu {
  background-color: var(--comfy-menu-bg) !important;
  color: var(--input-text);
}

.litegraph.litecontextmenu input {
  background-color: var(--comfy-input-bg) !important;
  color: var(--input-text) !important;
}

.comfy-context-menu-filter {
  box-sizing: border-box;
  border: 1px solid #999;
  margin: 0 0 5px 5px;
  width: calc(100% - 10px);
}

.comfy-img-preview {
  pointer-events: none;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: center;
}

.comfy-img-preview img {
  object-fit: contain;
  width: var(--comfy-img-preview-width);
  height: var(--comfy-img-preview-height);
}

.comfy-img-preview video {
  pointer-events: auto;
  object-fit: contain;
  height: 100%;
  width: 100%;
}

.comfy-missing-nodes li button {
  font-size: 12px;
  margin-left: 5px;
}

/* Search box */

.litegraph.litesearchbox {
  z-index: 9999 !important;
  background-color: var(--comfy-menu-bg) !important;
  overflow: hidden;
  display: block;
}

.litegraph.litesearchbox input,
.litegraph.litesearchbox select {
  background-color: var(--comfy-input-bg) !important;
  color: var(--input-text);
}

.litegraph.lite-search-item {
  color: var(--input-text);
  background-color: var(--comfy-input-bg);
  filter: brightness(80%);
  will-change: transform;
  padding-left: 0.2em;
}

.litegraph.lite-search-item.generic_type {
  color: var(--input-text);
  filter: brightness(50%);
  will-change: transform;
}

audio.comfy-audio.empty-audio-widget {
  display: none;
}

#vue-app {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Set auto complete panel's width as it is not accessible within vue-root */
.p-autocomplete-overlay {
  max-width: 25vw;
}

.p-tree-node-content {
  padding: var(--comfy-tree-explorer-item-padding) !important;
}

/* Load3d styles */
.comfy-load-3d,
.comfy-load-3d-animation,
.comfy-preview-3d,
.comfy-preview-3d-animation{
  display: flex;
  flex-direction: column;
  background: transparent;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.comfy-load-3d canvas,
.comfy-load-3d-animation canvas,
.comfy-preview-3d canvas,
.comfy-preview-3d-animation canvas{
  display: flex;
  width: 100% !important;
  height: 100% !important;
}

/* End of Load3d styles */

/* [Desktop] Electron window specific styles */
.app-drag {
  app-region: drag;
}

.no-drag {
  app-region: no-drag;
}

.window-actions-spacer {
  width: calc(100vw - env(titlebar-area-width, 100vw));
}
/* End of [Desktop] Electron window specific styles */
