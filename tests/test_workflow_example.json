{"description": "Test workflow for artlet deadlock detection", "workflow": {"1": {"inputs": {"server_block_id": "test_server_block", "input_0": "test_input_data", "input_name_0": "test_input"}, "class_type": "ArtletServerInputBlock", "_meta": {"title": "Server Input Block - Test"}}, "2": {"inputs": {"server_block_id": "test_server_block", "output_0": ["1", 0], "output_name_0": "processed_output"}, "class_type": "ArtletServerOutputBlock", "_meta": {"title": "Server Output Block - Test"}}, "3": {"inputs": {"client_block_id": "test_client_block", "server_block_id": "test_server_block", "server_address": "http://localhost:8188", "input_0": "client_test_data", "api_file": "test_api.json"}, "class_type": "ArtletClientBlock", "_meta": {"title": "<PERSON><PERSON> Block - Test"}}}, "test_scenarios": [{"name": "Normal Operation", "description": "Test normal client-server communication", "expected_behavior": "Client sends data, server processes, client receives output"}, {"name": "Server Timeout", "description": "Test behavior when server takes very long to process", "expected_behavior": "Smart waiting should continue as long as server is actively processing"}, {"name": "Client Disconnect", "description": "Test behavior when client disconnects during processing", "expected_behavior": "Server should detect client disconnect and cleanup"}, {"name": "Deadlock Detection", "description": "Test deadlock detection when both sides are waiting", "expected_behavior": "System should detect deadlock and raise appropriate exception"}]}