"""
Custom exceptions for Artlet client-server communication and deadlock detection
"""

class ArtletException(Exception):
    """Base exception for all Artlet-related errors"""
    pass

class DeadlockException(ArtletException):
    """Raised when a deadlock is detected between client and server"""
    pass

class ClientDisconnectedException(ArtletException):
    """Raised when client has disconnected"""
    pass

class ServerUnavailableException(ArtletException):
    """Raised when server is not reachable"""
    pass

class ConnectionException(ArtletException):
    """Raised for general connection issues"""
    pass

class TimeoutException(ArtletException):
    """Raised when operation times out"""
    pass

class ExecutionException(ArtletException):
    """Raised when execution fails on server"""
    pass

class HeartbeatException(ArtletException):
    """Raised when heartbeat is stale or missing"""
    pass
