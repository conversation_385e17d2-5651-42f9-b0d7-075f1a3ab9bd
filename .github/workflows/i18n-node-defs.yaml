name: Update Node Definitions Locales

on:
  workflow_dispatch:
    inputs:
      trigger_type:
        description: 'Type of trigger (manual or automatic)'
        required: false
        type: string
        default: 'manual'

jobs:
  update-locales:
    runs-on: ubuntu-latest
    steps:
    - uses: Comfy-Org/ComfyUI_frontend_setup_action@v2.3
    - name: Install Playwright Browsers
      run: npx playwright install chromium --with-deps
      working-directory: ComfyUI_frontend
    - name: Start dev server
      # Run electron dev server as it is a superset of the web dev server
      # We do want electron specific UIs to be translated.
      run: npm run dev:electron &
      working-directory: ComfyUI_frontend
    - name: Update en.json
      run: npm run collect-i18n -- scripts/collect-i18n-node-defs.ts
      env:
        PLAYWRIGHT_TEST_URL: http://localhost:5173
      working-directory: ComfyUI_frontend
    - name: Update translations
      run: npm run locale
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      working-directory: ComfyUI_frontend
    - name: <PERSON><PERSON> Pull Request
      uses: peter-evans/create-pull-request@v7
      with:
        token: ${{ secrets.PR_GH_TOKEN }}
        commit-message: "Update locales for node definitions"
        title: "Update locales for node definitions"
        body: |
          Automated PR to update locales for node definitions

          This PR was created automatically by the frontend update workflow.
        branch: update-locales-node-defs-${{ github.event.inputs.trigger_type }}-${{ github.run_id }}
        base: main
        labels: dependencies
        path: ComfyUI_frontend