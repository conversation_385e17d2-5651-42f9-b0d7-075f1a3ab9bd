import os
import pickle
import logging
import random
from aiohttp import web
import nodes

from artlets.artlet_models import (
    Status, ExecutionStatus, ClientStatus, get_or_create_client, get_or_create_prompt,
    get_or_create_block, add_input_data, add_output_data,
    update_all_input_status, update_all_output_status, get_client_id_from_prompt_id,
    update_execution_status, update_client_status, get_pending_inputs,
    is_heartbeat_stale, get_heartbeat_age
)

class ArtletRoutes:
    def __init__(self, prompt_server):
        self.prompt_server = prompt_server
        self.routes = web.RouteTableDef()
        self.add_routes()

    def add_routes(self):
        """Add all Artlet routes to the server"""

        @self.routes.post("/artlet/prompt")
        async def post_artlet_prompt(request):
            logging.info("got artlet prompt")
            return await self.prompt_server.post_prompt(request)

        @self.routes.get("/artlet/object/{prompt_id}/{server_block_id}/{input_output}/{object_id}")
        async def get_artlet_objects(request):
            prompt_id = request.match_info.get("prompt_id", None)
            server_block_id = request.match_info.get("server_block_id", None)
            object_id = request.match_info.get("object_id", None)
            input_output = request.match_info.get("input_output", None)

            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")

            obj = None
            if input_output == 'input':
                obj = self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].inputs[object_id].data
            elif input_output == 'output':
                obj = self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].outputs[object_id].data
            else:
                return web.Response(status=404, text="Invalid input_output")
            print('got obj', type(obj))

            return web.Response(body=pickle.dumps(obj), content_type="application/octet-stream")

        @self.routes.get("/artlet/object_ids/{prompt_id}/{server_block_id}/{input_output}")
        async def get_artlet_object_ids(request):
            prompt_id = request.match_info.get("prompt_id", None)
            server_block_id = request.match_info.get("server_block_id", None)
            input_output = request.match_info.get("input_output", None)
            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")

            object_ids = []
            if input_output == 'input':
                object_ids = [id for id in self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].inputs.keys()]
            elif input_output == 'output':
                object_ids = [id for id in self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].outputs.keys()]
            else:
                return web.Response(status=404, text="Invalid input_output")
            return web.json_response(object_ids)

        @self.routes.post("/artlet/object/{prompt_id}/{server_block_id}/{input_output}/{object_id}")
        async def save_artlet_objects(request):
            prompt_id = request.match_info.get("prompt_id", None)
            server_block_id = request.match_info.get("server_block_id", None)
            object_id = request.match_info.get("object_id", None)
            input_output = request.match_info.get("input_output", None)
            data = await request.read()
            print('artlet object received for prompt_id:', prompt_id, 'server_block_id:', server_block_id, 'input_output:', input_output, 'object_id:', object_id, 'data len:', len(data))
            if not data:
                return web.Response(status=400, text="No data provided")

            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")

            tobject = pickle.loads(data)
            if input_output == 'input':
                add_input_data(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
            elif input_output == 'output':
                add_output_data(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
            else:
                return web.Response(status=404, text="Invalid input_output")

            return web.Response(status=200, text="Object saved successfully")

        @self.routes.get("/artlet/object_status/{prompt_id}/{server_block_id}/{input_output}")
        async def get_artlet_object_status(request):
            prompt_id = request.match_info.get("prompt_id", None)
            server_block_id = request.match_info.get("server_block_id", None)
            input_output = request.match_info.get("input_output", None)
            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")
            status = []
            if input_output == 'input':
                status = self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].all_input_status
            elif input_output == 'output':
                status = self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].all_output_status
            else:
                return web.Response(status=404, text="Invalid input_output")
            return web.json_response({'status': status})

        @self.routes.post("/artlet/object_status/{prompt_id}/{server_block_id}/{input_output}")
        async def set_artlet_object_status(request):
            prompt_id = request.match_info.get("prompt_id", None)
            server_block_id = request.match_info.get("server_block_id", None)
            input_output = request.match_info.get("input_output", None)
            data = await request.json()
            status = data.get('status', None)
            print('got update status', status, 'for prompt_id', prompt_id, 'server_block_id', server_block_id, 'input_output', input_output)
            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")
            if status is None:
                return web.Response(status=400, text="No status provided")
            if input_output == 'input':
                update_all_input_status(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], status)
            elif input_output == 'output':
                update_all_output_status(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], status)
            else:
                return web.Response(status=404, text="Invalid input_output")
            return web.Response(status=200)

        @self.routes.post("/artlet/prompt_remove/{prompt_id}/")
        async def delete_artlet_prompt(request):
            print('post_interrupt called via /prompt_remove')

            prompt_id = request.match_info.get("prompt_id", None)
            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")
            self.prompt_server.artlet_data.as_server[client_id].prompts.pop(prompt_id, None)

            nodes.interrupt_processing()

            return web.Response(status=200)

        @self.routes.get("/artlet/execution_status/{prompt_id}/{block_id}")
        async def get_execution_status(request):
            """Get execution status for a specific block"""
            prompt_id = request.match_info.get("prompt_id", None)
            block_id = request.match_info.get("block_id", None)

            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")

            try:
                block_data = self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[block_id]
                exec_info = block_data.execution_info

                response_data = {
                    "prompt_id": prompt_id,
                    "block_id": block_id,
                    "status": exec_info.execution_status,
                    "current_node": exec_info.current_node,
                    "waiting_for": exec_info.waiting_for,
                    "waiting_for_block": exec_info.waiting_for_block,
                    "last_heartbeat": exec_info.last_heartbeat,
                    "execution_progress": exec_info.execution_progress,
                    "estimated_remaining": exec_info.estimated_remaining,
                    "error_message": exec_info.error_message,
                    "start_time": exec_info.start_time
                }

                return web.json_response(response_data)

            except KeyError:
                return web.Response(status=404, text="Block not found")

        @self.routes.get("/artlet/client_status/{client_id}/{prompt_id}")
        async def get_client_status(request):
            """Get client status for a specific prompt"""
            client_id = request.match_info.get("client_id", None)
            prompt_id = request.match_info.get("prompt_id", None)

            # Check both as_server and as_client
            client_data = None
            if client_id in self.prompt_server.artlet_data.as_server:
                client_data = self.prompt_server.artlet_data.as_server[client_id]
            elif client_id in self.prompt_server.artlet_data.as_client:
                client_data = self.prompt_server.artlet_data.as_client[client_id]

            if client_data is None:
                return web.Response(status=404, text="Client not found")

            client_info = client_data.client_info

            response_data = {
                "client_id": client_id,
                "prompt_id": prompt_id,
                "status": client_info.client_status,
                "last_heartbeat": client_info.last_heartbeat,
                "blocks_pending": client_info.blocks_pending,
                "current_operation": client_info.current_operation,
                "last_sent_inputs": client_info.last_sent_inputs,
                "connection_start": client_info.connection_start
            }

            return web.json_response(response_data)

        @self.routes.get("/artlet/prompt_status/{prompt_id}")
        async def get_prompt_status(request):
            """Check if prompt is still active"""
            prompt_id = request.match_info.get("prompt_id", None)

            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.json_response({"active": False, "reason": "prompt_not_found"})

            try:
                prompt_data = self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id]

                response_data = {
                    "active": True,
                    "prompt_id": prompt_id,
                    "status": prompt_data.status,
                    "blocks_count": len(prompt_data.blocks),
                    "blocks_ready": sum(1 for block in prompt_data.blocks.values() if block.status == Status.READY),
                    "blocks_processing": sum(1 for block in prompt_data.blocks.values() if block.status == Status.PROCESSING),
                    "blocks_error": sum(1 for block in prompt_data.blocks.values() if block.status == Status.ERROR)
                }

                return web.json_response(response_data)

            except KeyError:
                return web.json_response({"active": False, "reason": "prompt_data_not_found"})

        @self.routes.post("/artlet/client_heartbeat/{client_id}")
        async def client_heartbeat(request):
            """Update client heartbeat"""
            client_id = request.match_info.get("client_id", None)

            # Check both as_server and as_client
            client_data = None
            if client_id in self.prompt_server.artlet_data.as_server:
                client_data = self.prompt_server.artlet_data.as_server[client_id]
            elif client_id in self.prompt_server.artlet_data.as_client:
                client_data = self.prompt_server.artlet_data.as_client[client_id]

            if client_data is None:
                return web.Response(status=404, text="Client not found")

            # Update heartbeat
            import time
            client_data.client_info.last_heartbeat = time.time()

            return web.Response(status=200, text="Heartbeat updated")

        @self.routes.post("/artlet/object_file/{prompt_id}/{server_block_id}/{input_output}/{object_id}")
        async def upload_artlet_object_file(request):
            """Handle large object uploads as files"""
            prompt_id = request.match_info.get("prompt_id", None)
            server_block_id = request.match_info.get("server_block_id", None)
            object_id = request.match_info.get("object_id", None)
            input_output = request.match_info.get("input_output", None)

            # Get client ID from prompt ID
            client_id = get_client_id_from_prompt_id(self.prompt_server.artlet_data, prompt_id, as_server=True)
            if client_id is None or client_id not in self.prompt_server.artlet_data.as_server:
                return web.Response(status=404, text="Client not found")

            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), "temp", prompt_id, server_block_id, input_output)
            os.makedirs(temp_dir, exist_ok=True)

            # Save uploaded file to temp directory
            reader = await request.multipart()
            field = await reader.next()

            if field is None:
                return web.Response(status=400, text="No file provided")

            filename = os.path.join(temp_dir, f"{object_id}.pickle")

            # Write file to disk
            with open(filename, 'wb') as f:
                while True:
                    chunk = await field.read_chunk()
                    if not chunk:
                        break
                    f.write(chunk)

            try:
                # Load the file and add it to the data structure
                with open(filename, 'rb') as f:
                    data = f.read()

                tobject = pickle.loads(data)
                if input_output == 'input':
                    add_input_data(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
                elif input_output == 'output':
                    add_output_data(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
                else:
                    return web.Response(status=404, text="Invalid input_output")

                # Clean up the temporary file
                os.remove(filename)

                return web.Response(status=200, text="Object saved successfully")
            except Exception as e:
                logging.error(f"Error processing uploaded file: {e}")
                return web.Response(status=500, text=f"Error processing file: {str(e)}")

    def handle_artlet_prompt_data(self, json_data, prompt, client_id):
        """Process Artlet-specific data in the prompt"""
        artlet_server_nodes = [k for k, v in prompt.items() if v["class_type"] == "ArtletServerInputBlock" or v["class_type"] == "ArtletServerOutputBlock"]
        artlet_client_nodes = [k for k, v in prompt.items() if v["class_type"] == "ArtletClientBlock"]
        #remove None values, and keep only unique values
        artlet_server_nodes = list(set(filter(None, artlet_server_nodes)))
        artlet_client_nodes = list(set(filter(None, artlet_client_nodes)))

        artlet_service = json_data.get('artlet_service', False)

        if len(artlet_server_nodes) > 0:
            print('got artlet_server_nodes', artlet_server_nodes)
            # For each server block set update_int value to random so that the block is executed again
            for artlet_node in artlet_server_nodes:
                prompt[artlet_node]['inputs']['update_int'] = random.random()

            client_data = get_or_create_client(self.prompt_server.artlet_data, client_id, as_server=True)
            self.prompt_server.artlet_data.as_server[client_id].is_artlet_instance = artlet_service

        if len(artlet_client_nodes) > 0:
            print('got artlet_client_nodes', artlet_client_nodes)
            client_data = get_or_create_client(self.prompt_server.artlet_data, client_id, as_server=False)
            self.prompt_server.artlet_data.as_client[client_id].is_artlet_instance = artlet_service

        return artlet_server_nodes, artlet_client_nodes

    def setup_artlet_prompt_data(self, prompt, client_id, prompt_id, artlet_server_nodes, artlet_client_nodes):
        """Setup Artlet prompt and blocks data before execution"""
        if len(artlet_server_nodes) > 0:
            get_or_create_prompt(self.prompt_server.artlet_data.as_server[client_id], prompt_id)
        if len(artlet_client_nodes) > 0:
            get_or_create_prompt(self.prompt_server.artlet_data.as_client[client_id], prompt_id)

        for artlet_node in artlet_server_nodes:
            get_or_create_block(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id],
                               prompt[artlet_node]['inputs']['server_block_id'])
            # Set input_output block status to PENDING
            update_all_input_status(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[prompt[artlet_node]['inputs']['server_block_id']],
                                   Status.PENDING)
            update_all_output_status(self.prompt_server.artlet_data.as_server[client_id].prompts[prompt_id].blocks[prompt[artlet_node]['inputs']['server_block_id']],
                                    Status.PENDING)

        for artlet_node in artlet_client_nodes:
            get_or_create_block(self.prompt_server.artlet_data.as_client[client_id].prompts[prompt_id],
                               prompt[artlet_node]['inputs']['client_block_id'])
            update_all_input_status(self.prompt_server.artlet_data.as_client[client_id].prompts[prompt_id].blocks[prompt[artlet_node]['inputs']['client_block_id']],
                                   Status.PENDING)
            update_all_output_status(self.prompt_server.artlet_data.as_client[client_id].prompts[prompt_id].blocks[prompt[artlet_node]['inputs']['client_block_id']],
                                    Status.PENDING)


import uuid
import time
from artlets.split_artlet_prompts import split_artlet_prompts, save_prompt
import copy

def process_artlet_prompt_generators(prompt, client_id): 
    """
    Process ArtletPromptGenerator nodes in a prompt, generating client and server prompts.

    Args:
        prompt (dict): The original prompt JSON

    Returns:
        tuple: (run_nodes_on_server, save_prompts, client_prompt, client_prompt_file, [(server_prompt, server_prompt_file), ...])
    """
    mes = {
        "exception_message": f"No ArtletPromptGenerator nodes found in the prompt",
        "exception_type": "ExecutionBlocked",
        "prompt_id": '',"node_id": '',"node_type": '',"executed": [],"traceback": [],"current_inputs": [],"current_outputs": [],
    }

    return_dict = { 'run_nodes_on_server': False, 'save_prompts': False, 
            'client_prompt': prompt, 'server_prompts': [], 
            'client_api_file': None, 'server_api_files': [], 'mes':mes, 'error':'' }

    # Check if there are any ArtletPromptGenerator node
    def return_error(error_message):
        return_dict['mes']['exception_message'] = error_message
        return_dict['error'] = error_message
        return_dict['save_prompts'] = True
        return return_dict
    
    artlet_generator_nodes = {}
    for node_id, node_data in prompt.items():
        if node_data.get("class_type") == "ArtletPromptGenerator":
            # Ensure inputs are not links from other nodes
            has_link_inputs = False
            for input_name, input_value in node_data.get("inputs", {}).items():
                if isinstance(input_value, list) and len(input_value) == 2:
                    has_link_inputs = True
                    break

            if not has_link_inputs:
                artlet_generator_nodes[node_id] = node_data

    if not artlet_generator_nodes:
        # No ArtletPromptGenerator nodes found
        return return_dict

    #all the sequence numbers must be unique
    sequence_ids = [node_data.get("inputs", {}).get("sequence_id", 0) for node_data in artlet_generator_nodes.values()]
    if len(sequence_ids) != len(set(sequence_ids)):
        return return_error('All sequence_ids for ArtletPromptGenerator nodes must be unique')
      
    # Sort generator nodes by sequence_id
    sorted_generator_nodes = sorted(
        artlet_generator_nodes.items(),
        key=lambda x: x[1].get("inputs", {}).get("sequence_id", 0)
    )

    # Process each generator node
    client_prompt = prompt
    server_prompts = []
    client_prompt_file = None
    first_run_nodes_on_server =  sorted_generator_nodes[0][1].get('inputs',{}).get('run_nodes_on_server', False)
    first_save_prompts = sorted_generator_nodes[0][1].get('inputs',{}).get('save_prompts', False)
    first_client_api_file = sorted_generator_nodes[0][1].get('inputs',{}).get('client_api_file', None)

    if first_run_nodes_on_server is False and first_save_prompts is False :
        return return_error('Either run_node_on_server or save_prompts must be true')

    if first_run_nodes_on_server is True and first_save_prompts is True :
        return return_error('Only 1 of run_node_on_server or save_prompts must be true')
    #only 1 is tru if save_prompts change error message
    if first_save_prompts is True:
        return_dict['mes']['exception_message'] = 'Server and Client Prompts are saved appropriately'
    server_api_files = []

    try :
        for node_id, node_data in sorted_generator_nodes:
            inputs = node_data.get("inputs", {})
        # Extract parameters
            server_node_list = inputs.get("server_node_list", "")
            run_nodes_on_server = inputs.get("run_nodes_on_server", False)
            save_prompts = inputs.get("save_prompts", False)
            client_block_id = inputs.get("client_block_id", "client_block_1")
            server_block_id = inputs.get("server_block_id", "server_block_1")
            server_address = inputs.get("server_address", "http://localhost:18994")
            single_prompt_block = inputs.get("single_prompt_block", True)
            update_int = inputs.get("update_int", 0)
            sequence_id = inputs.get("sequence_id", 0)

            #check in all the nodes save_prompts and  run_node_on_server are same as first
            if run_nodes_on_server != first_run_nodes_on_server or save_prompts != first_save_prompts:
                return return_error('run_node_on_server and save_prompts must be same for all ArtletPromptGenerator nodes')
        
            # timestamp = int(time.time())
            # unique_id = str(uuid.uuid4())[:8]

            if save_prompts:
                # Use specified paths
                server_api_file = inputs.get("server_api_file", "")
                client_api_file = first_client_api_file
                if server_api_file == "" or client_api_file == "":
                    return return_error('server_api_file and client_api_file must be specified when save_prompts is True')
                # add artlet/  in the beginning of the path
                server_api_file = "artlet/" + server_api_file
                client_api_file = "artlet/" + client_api_file
                # Ensure directories exist
                os.makedirs(os.path.dirname(server_api_file), exist_ok=True)
                os.makedirs(os.path.dirname(client_api_file), exist_ok=True)
            else:
                # Generate temporary paths
                os.makedirs("/tmp", exist_ok=True)
                server_api_file = f"/tmp/server_prompt_{client_id}_{sequence_id}.json"
                client_api_file = f"/tmp/client_prompt_{client_id}.json"
            # remove starting artlet from server_api_file as code assumes all files in artlet directory
            server_api_file_in_node = server_api_file.replace("artlet/", "")
            client_api_file_in_node = client_api_file.replace("artlet/","")

            # Split the prompt
            new_client_prompt, server_prompt = split_artlet_prompts(
                client_prompt, server_node_list,
                client_block_id, server_block_id,
                server_address, single_prompt_block,
                server_api_file_in_node, client_api_file_in_node,
                sequence_id,
                update_int,
                run_nodes_on_server
            )

            #remove all the nodes matching class_type ArtletPromptGenerator from server_prompt
            server_prompt = {k: v for k, v in server_prompt.items() if v.get("class_type") != "ArtletPromptGenerator"}
            
            # Update client prompt for next iteration
            client_prompt = new_client_prompt
            #save only server prompt
            save_prompt(server_prompt, server_api_file)
            # Add server prompt to list
            server_prompts.append(server_prompt)
            server_api_files.append(server_api_file)
        
        #remove all the nodes matching class_type ArtletPromptGenerator from client_prompt
        client_prompt = {k: v for k, v in client_prompt.items() if v.get("class_type") != "ArtletPromptGenerator"}
        #finally save client prompt
        save_prompt(client_prompt, client_api_file)
        return { 'run_nodes_on_server': run_nodes_on_server, 'save_prompts': save_prompts, 
                'client_prompt': client_prompt, 'server_prompts': server_prompts, 
                'client_api_file': client_api_file, 'server_api_files': server_api_files, 'mes':mes, 'error':'' }
    except Exception as e:
        return return_error(f'Error processing ArtletPromptGenerator nodes: {str(e)}')

from comfy.cli_args import args, RunMode
def disable_client_mode_nodes(NODE_CLASS_MAPPINGS):
    if args.run_mode == RunMode.CLIENT:
        #running in client mode disable any nodes that have SERVER_ONLY set to true
        node_class_list = list(NODE_CLASS_MAPPINGS.keys())
        print("Running in client mode, disabling following server nodes")
        for node_class_name in node_class_list:
            node_class = NODE_CLASS_MAPPINGS[node_class_name]
            if hasattr(node_class, 'SERVER_ONLY') and node_class.SERVER_ONLY:
                NODE_CLASS_MAPPINGS.pop(node_class_name)
                print(node_class_name)


#old artlet implementation code from server.py

# from artlets.artlet_models import (
#     ArtletData, Status, get_or_create_client, get_or_create_prompt,
#     get_or_create_block, add_input_data, add_output_data, update_block_status, update_prompt_status,
#     update_all_input_status, update_all_output_status, get_client_id_from_prompt_id,  extract_auth_headers
# )

# def parse_server_address( server_address):
#     """Parse server address to extract protocol, host and port"""
#     protocol = "http"
#     host = None
#     port = None

#     if server_address:
#         # Check if protocol is specified
#         if "://" in server_address:
#             parts = server_address.split("://")
#             protocol = parts[0]
#             server_address = parts[1]

#         # Extract host and port
#         if ":" in server_address:
#             host, port = server_address.split(":", 1)
#         else:
#             host = server_address
#             port = "80" if protocol == "http" else "443"

#     return protocol, host, port


    # def cleanup_artlet_client_instance(self, client_prompt_id):
    #     """Clean up artlet client instance by removing prompt from server and closing websocket"""
    #     print('cleanup_artlet_client_instance called with client_prompt_id', client_prompt_id)
    #     if client_prompt_id is None:
    #         return

    #     client_client_id = get_client_id_from_prompt_id(self.artlet_data, client_prompt_id, as_server=False)
    #     if client_client_id is None or client_client_id not in self.artlet_data.as_client:
    #         return

    #     # Get server details
    #     server_prompt_id = self.artlet_data.as_client[client_client_id].prompts[client_prompt_id].server_prompt_id
    #     server_address = self.artlet_data.as_client[client_client_id].prompts[client_prompt_id].server_address

    #     # Get auth headers
    #     client_data = self.artlet_data.as_client[client_client_id]
    #     auth_headers = client_data.auth_headers if hasattr(client_data, 'auth_headers') else {}

    #     if server_prompt_id is not None and server_address is not None:
    #         protocol, host, port = parse_server_address(server_address)

    #         # Try to send cleanup request multiple times
    #         for i in range(5):
    #             time.sleep(0.1)
    #             try:
    #                 print(f'sending clean up to server and interrupt {i}th time')
    #                 url = f"{protocol}://{host}:{port}/artlet/prompt_remove/{server_prompt_id}/"

    #                 # Create request with auth headers
    #                 req = urllib.request.Request(url, headers=auth_headers, method="POST")

    #                 # Send request and check response status
    #                 response = urllib.request.urlopen(req)
    #                 status = response.status

    #                 # Only try to read response if there's content
    #                 content_length = response.getheader('Content-Length')
    #                 if content_length and int(content_length) > 0:
    #                     result = response.read()
    #                     print('result', result)
    #                 else:
    #                     print(f'Cleanup request successful (status {status}), no content returned')

    #                 # If successful, break the retry loop
    #                 if status == 200:
    #                     print('Cleanup request successful')
    #                     # break

    #             except Exception as e:
    #                 logging.warning(f"Failed to send clean up to server (attempt {i+1}/5): {e}")

    #     elif server_address is not None or server_prompt_id is not None:
    #         # Only 1 is present, which should not be the case
    #         print('Error: Missing server_address or server_prompt_id')
    #     else:
    #         # This is not a client instance
    #         print('This is not a client instance')

    #     # Close the websocket
    #     try:
    #         ws = self.artlet_data.as_client[client_client_id].prompts[client_prompt_id].websocket
    #         if ws is not None:
    #             print('Closing websocket')
    #             ws.close()
    #     except Exception as e:
    #         print(f'Error closing websocket: {e}')

    #     # Remove the data from client prompt
    #     self.artlet_data.as_client[client_client_id].prompts.pop(client_prompt_id, None)
    #     print('cleanup_artlet_client_instance complete', list(self.artlet_data.as_client[client_client_id].prompts.keys()))
    #     return





#following code is from PromptServer class __init__

# from artlets.artlet_models import (
#     ArtletData, Status, get_or_create_client, get_or_create_prompt,
#     get_or_create_block, add_input_data, add_output_data, update_block_status, update_prompt_status,
#     update_all_input_status, update_all_output_status, get_client_id_from_prompt_id,  extract_auth_headers
# )

# def parse_server_address( server_address):
#     """Parse server address to extract protocol, host and port"""
#     protocol = "http"
#     host = None
#     port = None

#     if server_address:
#         # Check if protocol is specified
#         if "://" in server_address:
#             parts = server_address.split("://")
#             protocol = parts[0]
#             server_address = parts[1]

#         # Extract host and port
#         if ":" in server_address:
#             host, port = server_address.split(":", 1)
#         else:
#             host = server_address
#             port = "80" if protocol == "http" else "443"

#     return protocol, host, port




        # @routes.get("/artlet/object/{prompt_id}/{server_block_id}/{input_output}/{object_id}")
        # async def get_artlet_objects(request):
        #     prompt_id = request.match_info.get("prompt_id", None)
        #     server_block_id = request.match_info.get("server_block_id", None)
        #     object_id = request.match_info.get("object_id", None)
        #     input_output = request.match_info.get("input_output", None)

        #     #given prompt get client_id
        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")

        #     obj = None
        #     if input_output == 'input':
        #         obj = self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].inputs[object_id].data
        #     elif input_output == 'output':
        #         obj = self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].outputs[object_id].data
        #     else :
        #         return web.Response(status=404, text="Invalid input_output")
        #     print('got obj', type(obj))

        #     return web.Response(body=pickle.dumps(obj), content_type="application/octet-stream")

        # @routes.get("/artlet/object_ids/{prompt_id}/{server_block_id}/{input_output}")
        # async def get_artlet_object_ids(request):
        #     prompt_id = request.match_info.get("prompt_id", None)
        #     server_block_id = request.match_info.get("server_block_id", None)
        #     input_output = request.match_info.get("input_output", None)
        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")

        #     object_ids = []
        #     if input_output == 'input':
        #         object_ids = [id for id in self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].inputs.keys()]
        #     elif input_output == 'output':
        #         object_ids = [id for id in self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].outputs.keys()]
        #     else :
        #         return web.Response(status=404, text="Invalid input_output")
        #     return web.json_response(object_ids)

        # @routes.post("/artlet/object/{prompt_id}/{server_block_id}/{input_output}/{object_id}")
        # async def save_artlet_objects(request):
        #     prompt_id = request.match_info.get("prompt_id", None)
        #     server_block_id = request.match_info.get("server_block_id", None)
        #     object_id = request.match_info.get("object_id", None)
        #     input_output = request.match_info.get("input_output", None)
        #     data = await request.read()
        #     print('artlet object received for prompt_id:', prompt_id, 'server_block_id:', server_block_id, 'input_output:', input_output, 'object_id:', object_id, 'data len:', len(data))
        #     if not data:
        #         return web.Response(status=400, text="No data provided")

        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")

        #     tobject = pickle.loads(data)
        #     if input_output == 'input':
        #         add_input_data(self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
        #     elif input_output == 'output':
        #         add_output_data(self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
        #     else :
        #         return web.Response(status=404, text="Invalid input_output")

        #     return web.Response(status=200, text="Object saved successfully")

        # @routes.get("/artlet/object_status/{prompt_id}/{server_block_id}/{input_output}")
        # async def get_artlet_object_status(request):
        #     prompt_id = request.match_info.get("prompt_id", None)
        #     server_block_id = request.match_info.get("server_block_id", None)
        #     input_output = request.match_info.get("input_output", None)
        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")
        #     status = []
        #     if input_output == 'input':
        #         status = self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].all_input_status
        #     elif input_output == 'output':
        #         status = self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id].all_output_status
        #     else :
        #         return web.Response(status=404, text="Invalid input_output")
        #     return web.json_response({'status': status})

        # @routes.post("/artlet/object_status/{prompt_id}/{server_block_id}/{input_output}")
        # async def set_artlet_object_status(request):
        #     prompt_id = request.match_info.get("prompt_id", None)
        #     server_block_id = request.match_info.get("server_block_id", None)
        #     input_output = request.match_info.get("input_output", None)
        #     data = await request.json()
        #     status = data.get('status', None)
        #     print('got update status', status, 'for prompt_id', prompt_id, 'server_block_id', server_block_id, 'input_output', input_output)
        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")
        #     if status is None:
        #         return web.Response(status=400, text="No status provided")
        #     if input_output == 'input':
        #         update_all_input_status(self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], status)
        #     elif input_output == 'output':
        #         update_all_output_status(self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], status)
        #     else :
        #         return web.Response(status=404, text="Invalid input_output")
        #     return web.Response(status=200)

        # @routes.post("/artlet/prompt_remove/{prompt_id}/")
        # async def delete_artlet_prompt(request):
        #     print('post_interrupt called via /prompt_remove')

        #     prompt_id = request.match_info.get("prompt_id", None)
        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")
        #     self.artlet_data.as_server[client_id].prompts.pop(prompt_id, None)

        #     nodes.interrupt_processing()

        #     return web.Response(status=200)


        # @routes.post("/artlet/object_file/{prompt_id}/{server_block_id}/{input_output}/{object_id}")
        # async def upload_artlet_object_file(request):
        #     """Handle large object uploads as files"""
        #     prompt_id = request.match_info.get("prompt_id", None)
        #     server_block_id = request.match_info.get("server_block_id", None)
        #     object_id = request.match_info.get("object_id", None)
        #     input_output = request.match_info.get("input_output", None)

        #     # Get client ID from prompt ID
        #     client_id = get_client_id_from_prompt_id(self.artlet_data, prompt_id, as_server=True)
        #     if client_id is None or client_id not in self.artlet_data.as_server:
        #         return web.Response(status=404, text="Client not found")

        #     # Create temp directory if it doesn't exist
        #     temp_dir = os.path.join(os.getcwd(), "temp", prompt_id, server_block_id, input_output)
        #     os.makedirs(temp_dir, exist_ok=True)

        #     # Save uploaded file to temp directory
        #     reader = await request.multipart()
        #     field = await reader.next()

        #     if field is None:
        #         return web.Response(status=400, text="No file provided")

        #     filename = os.path.join(temp_dir, f"{object_id}.pickle")

        #     # Write file to disk
        #     with open(filename, 'wb') as f:
        #         while True:
        #             chunk = await field.read_chunk()
        #             if not chunk:
        #                 break
        #             f.write(chunk)

        #     # Load the object from the file
        #     try:
        #         with open(filename, 'rb') as f:
        #             tobject = pickle.load(f)

        #         # Add the object to the appropriate collection
        #         if input_output == 'input':
        #             add_input_data(self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
        #         elif input_output == 'output':
        #             add_output_data(self.artlet_data.as_server[client_id].prompts[prompt_id].blocks[server_block_id], object_id, tobject)
        #         else:
        #             return web.Response(status=404, text="Invalid input_output")

        #         # Clean up the temporary file
        #         os.remove(filename)

        #         return web.Response(status=200, text="Object saved successfully")
        #     except Exception as e:
        #         logging.error(f"Error processing uploaded file: {e}")
        #         return web.Response(status=500, text=f"Error processing file: {str(e)}")
