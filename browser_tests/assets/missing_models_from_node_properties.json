{"last_node_id": 1, "last_link_id": 1, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [256, 256], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": null}, {"name": "CLIP", "type": "CLIP", "links": null}, {"name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "fake_model.safetensors", "url": "http://localhost:8188/api/devtools/fake_model.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["fake_model.safetensors"]}], "links": [], "groups": [], "config": {}, "extra": {"ds": {"offset": [0, 0], "scale": 1}}, "version": 0.4}