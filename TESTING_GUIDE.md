# Artlet Deadlock Detection - Testing Guide

## Overview
This guide helps you test the new deadlock detection and smart waiting functionality in qsComfy.

## Quick Start

### 1. Automated Testing
```bash
# Run all tests
python run_artlet_tests.py

# Run only unit tests
python run_artlet_tests.py --unit

# Run only integration tests  
python run_artlet_tests.py --integration
```

### 2. Manual Testing with Running Server

#### Start the Server
```bash
python main.py
```

#### Test New Endpoints
```bash
# Test execution status endpoint
curl http://localhost:8188/artlet/execution_status/test_prompt/test_block

# Test client status endpoint
curl http://localhost:8188/artlet/client_status/test_client/test_prompt

# Test prompt status endpoint
curl http://localhost:8188/artlet/prompt_status/test_prompt

# Test client heartbeat endpoint
curl -X POST http://localhost:8188/artlet/client_heartbeat/test_client
```

## Testing Scenarios

### Scenario 1: Normal Operation
**Goal**: Verify smart waiting works during normal operation

**Steps**:
1. Set up a simple client-server workflow
2. Use ArtletClientBlock to send data to ArtletServerInputBlock
3. Monitor logs for smart waiting messages
4. Verify execution status updates

**Expected**: 
- Client shows "Smart waiting for outputs from server block"
- Server shows "Smart waiting for inputs for block"
- Status updates show proper execution phases

### Scenario 2: Long-Running Operations
**Goal**: Verify no false timeouts during legitimate long operations

**Steps**:
1. Create a server workflow with a slow node (e.g., large image processing)
2. Set up client to wait for results
3. Monitor that client doesn't timeout while server is actively processing

**Expected**:
- Client continues waiting as long as server execution_status is "executing"
- No timeout errors during active processing
- Heartbeat updates continue

### Scenario 3: Server Disconnect
**Goal**: Test behavior when server becomes unavailable

**Steps**:
1. Start client-server communication
2. Stop the server during processing
3. Observe client behavior

**Expected**:
- Client detects server unavailability
- ServerUnavailableException is raised
- Proper cleanup occurs

### Scenario 4: Client Disconnect  
**Goal**: Test behavior when client disconnects

**Steps**:
1. Start client-server communication
2. Kill client process during server processing
3. Observe server behavior

**Expected**:
- Server detects stale client heartbeat
- ClientDisconnectedException is raised
- Server stops waiting and cleans up

## Monitoring and Debugging

### Log Messages to Watch For

**Client Side**:
```
Smart waiting for outputs from server block {block_id}
Server execution status: executing/waiting_input/completed
Heartbeat age: X.XX seconds
```

**Server Side**:
```
Smart waiting for inputs for block {block_id}
Client status: connected/sending_data/waiting_response
Client heartbeat age: X.XX seconds
```

### Status Endpoint Responses

**Execution Status** (`/artlet/execution_status/{prompt_id}/{block_id}`):
```json
{
  "prompt_id": "abc123",
  "block_id": "server_block_1",
  "status": "executing",
  "current_node": "node_123",
  "waiting_for": [],
  "last_heartbeat": 1234567890.123,
  "execution_progress": 0.75
}
```

**Client Status** (`/artlet/client_status/{client_id}/{prompt_id}`):
```json
{
  "client_id": "client123", 
  "prompt_id": "abc123",
  "status": "waiting_response",
  "last_heartbeat": 1234567890.123,
  "current_operation": "waiting_for_output_block1"
}
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all new files are in the correct locations
   - Check Python path includes the artlets directory

2. **Endpoint Not Found (404)**
   - Verify server is running with updated code
   - Check that artlet_helpers.py has the new endpoints

3. **Timeout Still Occurring**
   - Check if execution status is being updated properly
   - Verify heartbeat mechanism is working
   - Look for error messages in logs

4. **Tests Failing**
   - Run `python run_artlet_tests.py --unit` to isolate unit test issues
   - Check that all dependencies are installed
   - Verify server is running for integration tests

### Debug Mode

Enable debug logging to see detailed execution flow:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Testing

### Load Testing
1. Set up multiple client-server pairs
2. Monitor resource usage and response times
3. Verify heartbeat mechanism scales properly

### Stress Testing  
1. Test with very long-running operations (hours)
2. Test with frequent client connects/disconnects
3. Test with network interruptions

## Next Steps

After verifying the current implementation works:

1. **Phase 2**: Enhanced deadlock detection algorithms
2. **Phase 3**: Automatic recovery mechanisms  
3. **Phase 4**: Advanced monitoring and metrics

## Reporting Issues

When reporting issues, include:
- Server and client logs
- Execution status endpoint responses
- Steps to reproduce
- Expected vs actual behavior
