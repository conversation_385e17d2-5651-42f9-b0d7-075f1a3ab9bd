{"last_node_id": 15, "last_link_id": 9, "nodes": [{"id": 15, "type": "workflow/hello", "pos": {"0": 566, "1": 316}, "size": {"0": 468.5999755859375, "1": 582}, "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": null, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": null, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": null, "label": "latent_image"}, {"name": "KSampler model", "type": "MODEL", "link": null, "label": "KSampler model"}, {"name": "<PERSON><PERSON><PERSON><PERSON> positive", "type": "CONDITIONING", "link": null, "label": "<PERSON><PERSON><PERSON><PERSON> positive"}, {"name": "<PERSON><PERSON><PERSON><PERSON> negative", "type": "CONDITIONING", "link": null, "label": "<PERSON><PERSON><PERSON><PERSON> negative"}, {"name": "KSampler latent_image", "type": "LATENT", "link": null, "label": "KSampler latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": null, "shape": 3, "label": "LATENT"}, {"name": "KSampler LATENT", "type": "LATENT", "links": null, "shape": 3, "label": "KSampler LATENT"}], "properties": {"Node name for S&R": "workflow/hello"}, "widgets_values": ["enable", 0, "randomize", 20, 8, "euler", "normal", 0, 10000, "disable", 0, "randomize", 20, 8, "euler", "normal", 1]}], "links": [], "groups": [], "config": {}, "extra": {"ds": {"offset": [0, 0], "scale": 1}, "groupNodes": {"hello": {"nodes": [{"id": -1, "type": "KSamplerAdvanced", "pos": {"0": 351.3332824707031, "1": 577.3333129882812}, "size": {"0": 315, "1": 334}, "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": null, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": null, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": null, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": null, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "KSamplerAdvanced"}, "widgets_values": ["enable", 0, "randomize", 20, 8, "euler", "normal", 0, 10000, "disable"], "index": 0}, {"id": -1, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 636, "1": 427}, "size": {"0": 315, "1": 262}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": null, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": null, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": null, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": null, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [0, "randomize", 20, 8, "euler", "normal", 1], "index": 1}], "links": [], "external": []}}}, "version": 0.4}