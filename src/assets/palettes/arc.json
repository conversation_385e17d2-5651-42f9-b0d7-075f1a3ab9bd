{"id": "arc", "name": "Arc", "colors": {"node_slot": {"BOOLEAN": "", "CLIP": "#eacb8b", "CLIP_VISION": "#A8DADC", "CLIP_VISION_OUTPUT": "#ad7452", "CONDITIONING": "#cf876f", "CONTROL_NET": "#00d78d", "CONTROL_NET_WEIGHTS": "", "FLOAT": "", "GLIGEN": "", "IMAGE": "#80a1c0", "IMAGEUPLOAD": "", "INT": "", "LATENT": "#b38ead", "LATENT_KEYFRAME": "", "MASK": "#a3bd8d", "MODEL": "#8978a7", "SAMPLER": "", "SIGMAS": "", "STRING": "", "STYLE_MODEL": "#C2FFAE", "T2I_ADAPTER_WEIGHTS": "", "TAESD": "#DCC274", "TIMESTEP_KEYFRAME": "", "UPSCALE_MODEL": "", "VAE": "#be616b"}, "litegraph_base": {"BACKGROUND_IMAGE": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAABcklEQVR4nO3YMUoDARgF4RfxBqZI6/0vZqFn0MYtrLIQMFN8U6V4LAtD+Jm9XG/v30OGl2e/AP7yevz4+vx45nvgF/+QGITEICQGITEIiUFIjNNC3q43u3/YnRJyPOzeQ+0e220nhRzReC8e7R7bbdvl+Jal1Bs46jEIiUFIDEJiEBKDkBhKPbZT6qHdptRTu02p53DUYxASg5AYhMQgJAYhMZR6bKfUQ7tNqad2m1LP4ajHICQGITEIiUFIDEJiKPXYTqmHdptST+02pZ7DUY9BSAxCYhASg5AYhMRQ6rGdUg/tNqWe2m1KPYejHoOQGITEICQGITEIiaHUYzulHtptSj2125R6Dkc9BiExCIlBSAxCYhASQ6nHdko9tNuUemq3KfUcjnoMQmIQEoOQGITEICSGUo/tlHpotyn11G5T6jkc9RiExCAkBiExCIlBSAylHtsp9dBuU+qp3abUczjqMQiJQUgMQmIQEoOQGITE+AHFISNQrFTGuwAAAABJRU5ErkJggg==", "CLEAR_BACKGROUND_COLOR": "#2b2f38", "NODE_TITLE_COLOR": "#b2b7bd", "NODE_SELECTED_TITLE_COLOR": "#FFF", "NODE_TEXT_SIZE": 14, "NODE_TEXT_COLOR": "#AAA", "NODE_SUBTEXT_SIZE": 12, "NODE_DEFAULT_COLOR": "#2b2f38", "NODE_DEFAULT_BGCOLOR": "#242730", "NODE_DEFAULT_BOXCOLOR": "#6e7581", "NODE_DEFAULT_SHAPE": 2, "NODE_BOX_OUTLINE_COLOR": "#FFF", "NODE_BYPASS_BGCOLOR": "#FF00FF", "NODE_ERROR_COLOUR": "#E00", "DEFAULT_SHADOW_COLOR": "rgba(0,0,0,0.5)", "DEFAULT_GROUP_FONT": 22, "WIDGET_BGCOLOR": "#2b2f38", "WIDGET_OUTLINE_COLOR": "#6e7581", "WIDGET_TEXT_COLOR": "#DDD", "WIDGET_SECONDARY_TEXT_COLOR": "#b2b7bd", "WIDGET_DISABLED_TEXT_COLOR": "#5c626d", "LINK_COLOR": "#9A9", "EVENT_LINK_COLOR": "#A86", "CONNECTING_LINK_COLOR": "#AFA"}, "comfy_base": {"fg-color": "#fff", "bg-color": "#2b2f38", "comfy-menu-bg": "#242730", "comfy-menu-secondary-bg": "#22252E", "comfy-input-bg": "#2b2f38", "input-text": "#ddd", "descrip-text": "#b2b7bd", "drag-text": "#ccc", "error-text": "#ff4444", "border-color": "#6e7581", "tr-even-bg-color": "#2b2f38", "tr-odd-bg-color": "#242730", "content-bg": "#6e7581", "content-fg": "#fff", "content-hover-bg": "#2b2f38", "content-hover-fg": "#fff", "bar-shadow": "rgba(8, 8, 8, 0.75) 0 0 0.5rem"}}}