{"last_node_id": 2, "last_link_id": 1, "nodes": [{"id": 2, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 521.0906982421875, "1": 40.999996185302734, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": {"0": 315, "1": 262}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null}, {"name": "positive", "type": "CONDITIONING", "link": null}, {"name": "negative", "type": "CONDITIONING", "link": null}, {"name": "latent_image", "type": "LATENT", "link": null}, {"name": "steps", "type": "INT", "link": 1, "widget": {"name": "steps"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [0, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 1, "type": "PrimitiveNode", "pos": {"0": 15, "1": 46, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [446.96645387135936, 108.34243389566905], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1], "slot_index": 0, "widget": {"name": "steps"}}], "properties": {"Run widget replace on values": false}, "widgets_values": [20, "fixed"]}], "links": [[1, 1, 0, 2, 4, "INT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [0, 0]}}, "version": 0.4}