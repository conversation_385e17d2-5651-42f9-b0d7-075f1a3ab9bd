import asyncio
import json
import logging
import threading
import websocket
from typing import Dict, Any, Optional, Callable
import time


class ArtletMessageTransformer:
    """Transforms websocket messages from remote Artlet server to local frontend format"""
    
    def __init__(self, local_prompt_id: str, remote_prompt_id: str, 
                 local_client_id: str, remote_client_id: str, 
                 node_id_mapping: Dict[str, str]):
        self.local_prompt_id = local_prompt_id
        self.remote_prompt_id = remote_prompt_id
        self.local_client_id = local_client_id
        self.remote_client_id = remote_client_id
        self.node_id_mapping = node_id_mapping  # remote_node_id -> local_node_id
        
    def transform_message(self, event: str, data: Dict[str, Any]) -> tuple[str, Dict[str, Any]]:
        """Transform remote server message to local frontend format"""
        transformed_data = data.copy()
        
        # Transform prompt_id if present
        if 'prompt_id' in transformed_data:
            if transformed_data['prompt_id'] == self.remote_prompt_id:
                transformed_data['prompt_id'] = self.local_prompt_id
        
        # Transform node IDs if present
        if 'node' in transformed_data:
            remote_node_id = transformed_data['node']
            if remote_node_id in self.node_id_mapping:
                transformed_data['node'] = self.node_id_mapping[remote_node_id]
        
        if 'display_node' in transformed_data:
            remote_display_node = transformed_data['display_node']
            if remote_display_node in self.node_id_mapping:
                transformed_data['display_node'] = self.node_id_mapping[remote_display_node]
        
        # Transform node lists (for execution_cached messages)
        if 'nodes' in transformed_data and isinstance(transformed_data['nodes'], list):
            transformed_nodes = []
            for remote_node_id in transformed_data['nodes']:
                if remote_node_id in self.node_id_mapping:
                    transformed_nodes.append(self.node_id_mapping[remote_node_id])
                else:
                    transformed_nodes.append(remote_node_id)  # Keep original if no mapping
            transformed_data['nodes'] = transformed_nodes
        
        # Transform executed list (for error messages)
        if 'executed' in transformed_data and isinstance(transformed_data['executed'], list):
            transformed_executed = []
            for remote_node_id in transformed_data['executed']:
                if remote_node_id in self.node_id_mapping:
                    transformed_executed.append(self.node_id_mapping[remote_node_id])
                else:
                    transformed_executed.append(remote_node_id)
            transformed_data['executed'] = transformed_executed
        
        # Transform node_id in error messages
        if 'node_id' in transformed_data:
            remote_node_id = transformed_data['node_id']
            if remote_node_id in self.node_id_mapping:
                transformed_data['node_id'] = self.node_id_mapping[remote_node_id]
        
        return event, transformed_data


class ArtletWebsocketProxy:
    """Proxies websocket messages from remote Artlet server to local ComfyUI frontend"""
    
    def __init__(self, server_instance, local_client_id: str, remote_websocket, 
                 transformer: ArtletMessageTransformer, send_proxy_func: Callable):
        self.server = server_instance
        self.local_client_id = local_client_id
        self.remote_ws = remote_websocket
        self.transformer = transformer
        self.send_proxy_func = send_proxy_func
        self.running = False
        self.proxy_thread = None
        
    def start_proxying(self):
        """Start listening to remote websocket and proxy messages"""
        if self.running:
            return
            
        self.running = True
        self.proxy_thread = threading.Thread(target=self._proxy_loop, daemon=True)
        self.proxy_thread.start()
        print(f"Started Artlet websocket proxy for client {self.local_client_id}")
        
    def stop_proxying(self):
        """Stop proxying and cleanup"""
        if not self.running:
            return
            
        self.running = False
        if self.remote_ws:
            try:
                self.remote_ws.close()
            except Exception as e:
                print(f"Error closing remote websocket: {e}")
        
        if self.proxy_thread and self.proxy_thread.is_alive():
            self.proxy_thread.join(timeout=5.0)
            
        print(f"Stopped Artlet websocket proxy for client {self.local_client_id}")
        
    def _proxy_loop(self):
        """Main proxy loop that receives and forwards messages"""
        try:
            while self.running and self.remote_ws:
                try:
                    # Set a timeout to allow checking self.running periodically
                    self.remote_ws.settimeout(1.0)
                    message = self.remote_ws.recv()
                    
                    if not message:
                        continue
                        
                    if isinstance(message, str):
                        try:
                            parsed_message = json.loads(message)
                            event = parsed_message.get('type')
                            data = parsed_message.get('data', {})
                            
                            if event:
                                # Transform the message for local frontend
                                transformed_event, transformed_data = self.transformer.transform_message(event, data)
                                
                                # Send to local frontend using the proxy function
                                self.send_proxy_func(transformed_event, transformed_data, self.local_client_id)
                                
                                print(f"Proxied message: with data keys{data.keys()}")
                                pass
                            
                        except json.JSONDecodeError as e:
                            print(f"Failed to parse websocket message: {e}")
                            
                    elif isinstance(message, (bytes, bytearray)):
                        # Handle binary messages (like preview images, thumbnails, etc.)
                        try:
                            # Binary messages in ComfyUI websockets typically have a specific format
                            # They often start with a JSON header followed by binary data
                            # We need to check if this is a structured binary message or raw binary

                            if len(message) > 8:  # Minimum size for structured message
                                # Try to parse as structured binary message
                                # ComfyUI binary messages often have format: [4-byte length][JSON][binary data]
                                try:
                                    # Check if it starts with a length prefix (common pattern)
                                    import struct
                                    if len(message) >= 4:
                                        # Try to read length prefix
                                        length_bytes = message[:4]
                                        json_length = struct.unpack('<I', length_bytes)[0]  # Little-endian unsigned int

                                        if json_length > 0 and json_length < len(message) - 4:
                                            # Extract JSON part
                                            json_part = message[4:4+json_length]
                                            binary_part = message[4+json_length:]

                                            # Parse JSON metadata
                                            json_str = json_part.decode('utf-8')
                                            metadata = json.loads(json_str)

                                            # Transform the metadata using our transformer
                                            event = metadata.get('type', 'binary_data')
                                            data = metadata.get('data', {})
                                            transformed_event, transformed_data = self.transformer.transform_message(event, data)

                                            # Reconstruct the binary message with transformed metadata
                                            transformed_json = json.dumps({
                                                'type': transformed_event,
                                                'data': transformed_data
                                            }).encode('utf-8')

                                            # Rebuild the binary message
                                            new_length = len(transformed_json)
                                            new_length_bytes = struct.pack('<I', new_length)
                                            transformed_message = new_length_bytes + transformed_json + binary_part

                                            # Send the transformed binary message
                                            self.send_proxy_func('binary_message', transformed_message, self.local_client_id)

                                            logging.debug(f"Proxied structured binary message: {transformed_event} for client {self.local_client_id}")
                                            continue

                                except (struct.error, json.JSONDecodeError, UnicodeDecodeError):
                                    # Not a structured message, treat as raw binary
                                    pass

                            # If we get here, it's a raw binary message
                            # Forward it as-is since it doesn't contain node/prompt IDs to transform
                            self.send_proxy_func('binary_data', message, self.local_client_id)
                            logging.debug(f"Proxied raw binary message ({len(message)} bytes) for client {self.local_client_id}")

                        except Exception as e:
                            logging.error(f"Error processing binary message: {e}")
                            # Still try to forward the raw message
                            try:
                                self.send_proxy_func('binary_data', message, self.local_client_id)
                            except Exception as e2:
                                logging.error(f"Failed to forward binary message: {e2}")
                    else:
                        print(f"Received unexpected message type from remote server: {type(message)}")
                        
                except websocket.WebSocketTimeoutException:
                    # Timeout is expected, continue loop
                    continue
                except websocket.WebSocketConnectionClosedException:
                    print("Remote websocket connection closed")
                    break
                except Exception as e:
                    print(f"Error in websocket proxy loop: {e}")
                    break
                    
        except Exception as e:
            print(f"Fatal error in websocket proxy: {e}")
        finally:
            self.running = False


def create_artlet_websocket_proxy(server_instance, local_client_id: str, local_prompt_id: str,
                                remote_websocket, remote_prompt_id: str, remote_client_id: str,
                                node_id_mapping: Dict[str, str], send_proxy_func: Callable) -> ArtletWebsocketProxy:
    """Factory function to create an Artlet websocket proxy"""
    
    transformer = ArtletMessageTransformer(
        local_prompt_id=local_prompt_id,
        remote_prompt_id=remote_prompt_id,
        local_client_id=local_client_id,
        remote_client_id=remote_client_id,
        node_id_mapping=node_id_mapping
    )
    
    proxy = ArtletWebsocketProxy(
        server_instance=server_instance,
        local_client_id=local_client_id,
        remote_websocket=remote_websocket,
        transformer=transformer,
        send_proxy_func=send_proxy_func
    )
    
    return proxy
