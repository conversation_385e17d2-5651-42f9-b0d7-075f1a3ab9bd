name: Vitest Tests

on:
  push:
    branches: [ main, master, dev*, core/*, desktop/* ]
  pull_request:
    branches: [ main, master, dev*, core/*, desktop/* ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'

    - name: Install dependencies
      run: npm ci

    - name: Run Vitest tests
      run: |
        npm run test:component
        npm run test:unit
