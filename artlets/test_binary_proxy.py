#!/usr/bin/env python3
"""
Test script for Artlet binary message proxying functionality
"""

import json
import struct
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)

def test_binary_message_parsing():
    """Test parsing of structured binary messages"""
    print("Testing binary message parsing...")
    
    # Create a mock structured binary message
    # Format: [4-byte length][JSON metadata][binary data]
    
    # Create JSON metadata
    metadata = {
        "type": "executing",
        "data": {
            "node": "remote_node_1",
            "prompt_id": "remote_456",
            "display_node": "remote_node_1"
        }
    }
    
    json_bytes = json.dumps(metadata).encode('utf-8')
    json_length = len(json_bytes)
    
    # Create some mock binary data (e.g., image data)
    binary_data = b'\x89PNG\r\n\x1a\n' + b'mock_image_data' * 100  # Mock PNG header + data
    
    # Construct the structured message
    length_bytes = struct.pack('<I', json_length)  # Little-endian unsigned int
    structured_message = length_bytes + json_bytes + binary_data
    
    print(f"Created structured message:")
    print(f"  Total length: {len(structured_message)} bytes")
    print(f"  JSON length: {json_length} bytes")
    print(f"  Binary data length: {len(binary_data)} bytes")
    print(f"  JSON metadata: {metadata}")
    
    # Test parsing logic (similar to what's in artlet_websocket_proxy.py)
    try:
        if len(structured_message) >= 4:
            # Read length prefix
            length_bytes_read = structured_message[:4]
            parsed_json_length = struct.unpack('<I', length_bytes_read)[0]
            
            print(f"  Parsed JSON length: {parsed_json_length}")
            
            if parsed_json_length > 0 and parsed_json_length < len(structured_message) - 4:
                # Extract JSON part
                json_part = structured_message[4:4+parsed_json_length]
                binary_part = structured_message[4+parsed_json_length:]
                
                # Parse JSON metadata
                json_str = json_part.decode('utf-8')
                parsed_metadata = json.loads(json_str)
                
                print(f"  Extracted JSON: {parsed_metadata}")
                print(f"  Binary part length: {len(binary_part)} bytes")
                print(f"  Binary part starts with: {binary_part[:20]}")
                
                # Verify the parsing worked correctly
                if parsed_metadata == metadata and binary_part == binary_data:
                    print("✅ Binary message parsing successful")
                    return True
                else:
                    print("❌ Binary message parsing failed - data mismatch")
                    return False
            else:
                print("❌ Invalid JSON length in binary message")
                return False
        else:
            print("❌ Binary message too short")
            return False
            
    except Exception as e:
        print(f"❌ Error parsing binary message: {e}")
        return False


def test_message_transformation():
    """Test transformation of binary message metadata"""
    print("\nTesting binary message transformation...")
    
    # Mock transformer (simplified version)
    class MockTransformer:
        def __init__(self):
            self.local_prompt_id = "local_123"
            self.remote_prompt_id = "remote_456"
            self.node_id_mapping = {
                "remote_node_1": "local_node_A",
                "remote_node_2": "local_node_B"
            }
        
        def transform_message(self, event, data):
            transformed_data = data.copy()
            
            # Transform prompt_id
            if 'prompt_id' in transformed_data:
                if transformed_data['prompt_id'] == self.remote_prompt_id:
                    transformed_data['prompt_id'] = self.local_prompt_id
            
            # Transform node IDs
            if 'node' in transformed_data:
                remote_node_id = transformed_data['node']
                if remote_node_id in self.node_id_mapping:
                    transformed_data['node'] = self.node_id_mapping[remote_node_id]
            
            if 'display_node' in transformed_data:
                remote_display_node = transformed_data['display_node']
                if remote_display_node in self.node_id_mapping:
                    transformed_data['display_node'] = self.node_id_mapping[remote_display_node]
            
            return event, transformed_data
    
    # Test transformation
    transformer = MockTransformer()
    
    original_metadata = {
        "type": "executing",
        "data": {
            "node": "remote_node_1",
            "prompt_id": "remote_456",
            "display_node": "remote_node_1"
        }
    }
    
    event, transformed_data = transformer.transform_message(
        original_metadata["type"], 
        original_metadata["data"]
    )
    
    expected_data = {
        "node": "local_node_A",
        "prompt_id": "local_123",
        "display_node": "local_node_A"
    }
    
    print(f"Original data: {original_metadata['data']}")
    print(f"Transformed data: {transformed_data}")
    print(f"Expected data: {expected_data}")
    
    if transformed_data == expected_data:
        print("✅ Binary message transformation successful")
        return True
    else:
        print("❌ Binary message transformation failed")
        return False


def test_binary_message_reconstruction():
    """Test reconstruction of transformed binary messages"""
    print("\nTesting binary message reconstruction...")
    
    # Original message components
    original_metadata = {
        "type": "executing",
        "data": {
            "node": "remote_node_1",
            "prompt_id": "remote_456"
        }
    }
    
    transformed_metadata = {
        "type": "executing",
        "data": {
            "node": "local_node_A",
            "prompt_id": "local_123"
        }
    }
    
    binary_data = b'mock_binary_data' * 50
    
    # Create original structured message
    original_json = json.dumps(original_metadata).encode('utf-8')
    original_length = len(original_json)
    original_message = struct.pack('<I', original_length) + original_json + binary_data
    
    # Simulate transformation and reconstruction
    transformed_json = json.dumps(transformed_metadata).encode('utf-8')
    new_length = len(transformed_json)
    new_length_bytes = struct.pack('<I', new_length)
    reconstructed_message = new_length_bytes + transformed_json + binary_data
    
    print(f"Original message length: {len(original_message)} bytes")
    print(f"Reconstructed message length: {len(reconstructed_message)} bytes")
    
    # Verify the reconstructed message can be parsed correctly
    try:
        # Parse the reconstructed message
        length_bytes = reconstructed_message[:4]
        json_length = struct.unpack('<I', length_bytes)[0]
        json_part = reconstructed_message[4:4+json_length]
        binary_part = reconstructed_message[4+json_length:]
        
        parsed_metadata = json.loads(json_part.decode('utf-8'))
        
        print(f"Parsed metadata from reconstructed message: {parsed_metadata}")
        print(f"Binary data preserved: {binary_part == binary_data}")
        
        if parsed_metadata == transformed_metadata and binary_part == binary_data:
            print("✅ Binary message reconstruction successful")
            return True
        else:
            print("❌ Binary message reconstruction failed")
            return False
            
    except Exception as e:
        print(f"❌ Error parsing reconstructed message: {e}")
        return False


def test_binary_event_types():
    """Test binary event type mapping"""
    print("\nTesting binary event type mapping...")
    
    # Test the mapping logic from send_artlet_proxy_sync
    def map_binary_event(event):
        if isinstance(event, str):
            if event == 'binary_message':
                return 1000  # BinaryEventTypes.ARTLET_BINARY_MESSAGE
            elif event == 'binary_data':
                return 1001  # BinaryEventTypes.ARTLET_BINARY_DATA
            else:
                return 1001  # Default to ARTLET_BINARY_DATA
        else:
            return event
    
    test_cases = [
        ('binary_message', 1000),
        ('binary_data', 1001),
        ('unknown_binary_type', 1001),
        (1005, 1005),  # Already an integer
    ]
    
    all_passed = True
    for event, expected in test_cases:
        result = map_binary_event(event)
        if result == expected:
            print(f"✅ {event} -> {result}")
        else:
            print(f"❌ {event} -> {result}, expected {expected}")
            all_passed = False
    
    return all_passed


if __name__ == "__main__":
    print("=== Artlet Binary Message Proxy Tests ===")
    
    tests = [
        test_binary_message_parsing,
        test_message_transformation,
        test_binary_message_reconstruction,
        test_binary_event_types
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Test Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("🎉 All binary message proxy tests passed!")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
