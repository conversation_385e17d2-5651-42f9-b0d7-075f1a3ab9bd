from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pydantic import BaseModel, Field
import time
import urllib.request
import logging


class Status(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"

class ExecutionStatus(str, Enum):
    EXECUTING = "executing"
    WAITING_INPUT = "waiting_input"
    WAITING_OUTPUT = "waiting_output"
    COMPLETED = "completed"
    ERROR = "error"
    IDLE = "idle"

class ClientStatus(str, Enum):
    CONNECTED = "connected"
    SENDING_DATA = "sending_data"
    WAITING_RESPONSE = "waiting_response"
    DISCONNECTED = "disconnected"


class ObjectData(BaseModel):
    id: str
    data: Any = None
    status: Status = Status.PENDING


class ExecutionInfo(BaseModel):
    execution_status: ExecutionStatus = ExecutionStatus.IDLE
    current_node: Optional[str] = None
    waiting_for: List[str] = Field(default_factory=list)
    waiting_for_block: Optional[str] = None
    last_heartbeat: float = Field(default_factory=time.time)
    execution_progress: Optional[float] = None
    estimated_remaining: Optional[int] = None
    error_message: Optional[str] = None
    start_time: float = Field(default_factory=time.time)

class ClientInfo(BaseModel):
    client_status: ClientStatus = ClientStatus.CONNECTED
    last_heartbeat: float = Field(default_factory=time.time)
    blocks_pending: List[str] = Field(default_factory=list)
    current_operation: Optional[str] = None
    last_sent_inputs: Optional[float] = None
    connection_start: float = Field(default_factory=time.time)

class BlockData(BaseModel):
    block_id: str
    inputs: Dict[str, ObjectData] = Field(default_factory=dict)
    outputs: Dict[str, ObjectData] = Field(default_factory=dict)
    all_input_status: Status = Status.PENDING
    all_output_status: Status = Status.PENDING
    status: Status = Status.PENDING
    server_block_id: Optional[str] = None
    execution_info: ExecutionInfo = Field(default_factory=ExecutionInfo)
 

class PromptData(BaseModel):
    prompt_id: str
    blocks: Dict[str, BlockData] = Field(default_factory=dict)
    status: Status = Status.PENDING
    api_file: Optional[str] = None
    server_address: Optional[str] = None
    websocket: Optional[Any] = None  # Can't type websocket properly
    server_prompt_id: Optional[str] = None #applicable only for client
    websocket_proxy: Optional[Any] = None  # Artlet websocket proxy instance
    node_id_mapping: Dict[str, str] = Field(default_factory=dict)  # remote_node_id -> local_node_id


class ClientData(BaseModel):
    client_id: str
    auth_data: Dict[str, Any] = Field(default_factory=dict)
    prompts: Dict[str, PromptData] = Field(default_factory=dict)
    is_artlet_instance: bool = False
    auth_headers: Dict[str, str] = Field(default_factory=dict)  # Store authentication headers
    ssl_context: Optional[Any] = None  # Store SSL context for future requests
    client_info: ClientInfo = Field(default_factory=ClientInfo)


class ArtletData(BaseModel):
    as_client: Dict[str, ClientData] = Field(default_factory=dict)
    as_server: Dict[str, ClientData] = Field(default_factory=dict)


# Helper functions to work with the data structure
def get_or_create_client(artlet_data: ArtletData, client_id: str, as_server: bool = False) -> ClientData:
    """Get or create a client entry in the artlet data structure"""
    target = artlet_data.as_server if as_server else artlet_data.as_client
    if client_id not in target:
        target[client_id] = ClientData(client_id=client_id)
    return target[client_id]


def get_or_create_prompt(client_data: ClientData, prompt_id: str) -> PromptData:
    """Get or create a prompt entry for a client"""
    if prompt_id not in client_data.prompts:
        client_data.prompts[prompt_id] = PromptData(prompt_id=prompt_id)
        #since its created set prompt status to processing
        client_data.prompts[prompt_id].status = Status.PROCESSING
    return client_data.prompts[prompt_id]


def get_or_create_block(prompt_data: PromptData, block_id: str) -> BlockData:
    """Get or create a block entry for a prompt"""
    if block_id not in prompt_data.blocks:
        prompt_data.blocks[block_id] = BlockData(block_id=block_id)
        #since its created set block status to processing
        prompt_data.blocks[block_id].status = Status.PROCESSING
        #for every block create 10 inputs and 10 outputs with data None and status pending
        for i in range(10):
            prompt_data.blocks[block_id].inputs[f"input_{i}"] = ObjectData(id=f"input_{i}")
            prompt_data.blocks[block_id].outputs[f"output_{i}"] = ObjectData(id=f"output_{i}")
    return prompt_data.blocks[block_id]


def add_input_data(block_data: BlockData, input_id: str, data: Any) -> None:
    """Add input data to a block"""
    if input_id not in block_data.inputs:
        block_data.inputs[input_id] = ObjectData(id=input_id)
    block_data.inputs[input_id].data = data
    block_data.inputs[input_id].status = Status.READY

def update_all_input_status(block_data: BlockData, status: Status) -> None:
    """Update all input status to a block"""
    for input_data in block_data.inputs.values():
        input_data.status = status
    block_data.all_input_status = status

def update_all_output_status(block_data: BlockData, status: Status) -> None:
    """Update all output status to a block"""
    for output_data in block_data.outputs.values():
        output_data.status = status
    block_data.all_output_status = status

def add_output_data(block_data: BlockData, output_id: str, data: Any) -> None:
    """Add output data to a block"""
    if output_id not in block_data.outputs:
        block_data.outputs[output_id] = ObjectData(id=output_id)
    block_data.outputs[output_id].data = data
    block_data.outputs[output_id].status = Status.READY


def update_block_status(block_data: BlockData) -> None:
    """Update block status based on inputs and outputs status"""
    if any(obj.status == Status.ERROR for obj in block_data.inputs.values()):
        block_data.status = Status.ERROR
    elif all(obj.status == Status.READY for obj in block_data.inputs.values()):
        block_data.status = Status.READY
    else:
        block_data.status = Status.PENDING


def update_prompt_status(prompt_data: PromptData) -> None:
    """Update prompt status based on blocks status"""
    if any(block.status == Status.ERROR for block in prompt_data.blocks.values()):
        prompt_data.status = Status.ERROR
    elif all(block.status == Status.READY for block in prompt_data.blocks.values()):
        prompt_data.status = Status.READY
    elif any(block.status == Status.PROCESSING for block in prompt_data.blocks.values()):
        prompt_data.status = Status.PROCESSING
    else:
        prompt_data.status = Status.PENDING
        
# prmpt_ids are unique given prompt_id get corresponding client_id

def get_client_id_from_prompt_id(artlet_data: ArtletData, prompt_id: str, as_server: bool = False) -> Optional[str]:
    """Get client ID from prompt ID"""
    if as_server:
        for client_id, client_data in artlet_data.as_server.items():
            if prompt_id in client_data.prompts:
                return client_id
    for client_id, client_data in artlet_data.as_client.items():
        if prompt_id in client_data.prompts:
            return client_id
    return None


def extract_auth_headers(request, client_data: ClientData) -> Dict[str, str]:
    """Extract authentication headers from a request"""
    print("Received headers:", dict(request.headers).keys())
    auth_headers = {}
    for k in ['X-Auth-Token', 'X-Auth-User', 'X-Auth-Email', 'Cookie', 'Authorization']:
        if k in request.headers:
            auth_headers[k] = request.headers[k]
    if not auth_headers:
        return {}
       # raise Exception("Missing token")
    print ('auth_headers', auth_headers.keys())
    
    client_data.auth_headers = auth_headers
    
    return auth_headers


def parse_server_address( server_address):
    """Parse server address to extract protocol, host and port"""
    protocol = "http"
    host = None
    port = None

    if server_address:
        # Check if protocol is specified
        if "://" in server_address:
            parts = server_address.split("://")
            protocol = parts[0]
            server_address = parts[1]

        # Remove trailing slash if present
        server_address = server_address.rstrip('/')

        # Extract host and port
        if ":" in server_address:
            host, port = server_address.split(":", 1)
        else:
            host = server_address
            port = "80" if protocol == "http" else "443"

    return protocol, host, port

def cleanup_artlet_client_instance(client_instance, client_prompt_id):
    """Clean up artlet client instance by removing prompt from server and closing websocket"""
    print('cleanup_artlet_client_instance called with client_prompt_id', client_prompt_id)
    if client_prompt_id is None:
        return
        
    client_client_id = get_client_id_from_prompt_id(client_instance.artlet_data, client_prompt_id, as_server=False)
    if client_client_id is None or client_client_id not in client_instance.artlet_data.as_client:
        return
    
    # Get server details
    server_prompt_id = client_instance.artlet_data.as_client[client_client_id].prompts[client_prompt_id].server_prompt_id
    server_address = client_instance.artlet_data.as_client[client_client_id].prompts[client_prompt_id].server_address
    
    # Get auth headers
    client_data = client_instance.artlet_data.as_client[client_client_id]
    auth_headers = client_data.auth_headers if hasattr(client_data, 'auth_headers') else {}

    # Get SSL context if available
    ssl_context = getattr(client_data, 'ssl_context', None)


    if server_prompt_id is not None and server_address is not None:
        protocol, host, port = parse_server_address(server_address)
        
        # Try to send cleanup request multiple times
        for i in range(5):
            time.sleep(0.1)
            try:
                print(f'sending clean up to server and interrupt {i}th time')
                url = f"{protocol}://{host}:{port}/artlet/prompt_remove/{server_prompt_id}/"
                
                # Create request with auth headers
                req = urllib.request.Request(url, headers=auth_headers, method="POST")
                
                # Send request and check response status
                if ssl_context:
                    response = urllib.request.urlopen(req, context=ssl_context)
                else:
                    response = urllib.request.urlopen(req)
                status = response.status
                
                # Only try to read response if there's content
                content_length = response.getheader('Content-Length')
                if content_length and int(content_length) > 0:
                    result = response.read()
                    print('result', result)
                else:
                    print(f'Cleanup request successful (status {status}), no content returned')
                
                # If successful, break the retry loop
                if status == 200:
                    print('Cleanup request successful')
                    # break
                    
            except Exception as e:
                logging.warning(f"Failed to send clean up to server (attempt {i+1}/5): {e}")
    
    elif server_address is not None or server_prompt_id is not None:
        # Only 1 is present, which should not be the case
        print('Error: Missing server_address or server_prompt_id')
    else:
        # This is not a client instance
        print('This is not a client instance')
    
    # Stop the websocket proxy and close the websocket
    try:
        prompt_data = client_instance.artlet_data.as_client[client_client_id].prompts[client_prompt_id]

        # Stop the websocket proxy if it exists
        if hasattr(prompt_data, 'websocket_proxy') and prompt_data.websocket_proxy is not None:
            print('Stopping websocket proxy')
            prompt_data.websocket_proxy.stop_proxying()
            prompt_data.websocket_proxy = None

        # Close the websocket
        if prompt_data.websocket is not None:
            print('Closing websocket')
            prompt_data.websocket.close()
            prompt_data.websocket = None

    except Exception as e:
        print(f'Error closing websocket/proxy: {e}')
    
    # Remove the data from client prompt
    client_instance.artlet_data.as_client[client_client_id].prompts.pop(client_prompt_id, None)
    print('cleanup_artlet_client_instance complete', list(client_instance.artlet_data.as_client[client_client_id].prompts.keys()))
    return


#old code from execution.py

        # self.data_store = {}
        # self.server_data_store = {}
        # self.server_object_status = {}

    # def cleanup_artlet_data(self, prompt_id): 
    #     self.server_data_store.pop(prompt_id, None)
    #     self.server_object_status.pop(prompt_id, None)

    # def clear_binary_objects(self, prompt_id):
    #     self.data_store.pop(prompt_id, None)

    # def add_binary_server_object(self, prompt_id, server_block_id, input_output, object_id, obj):
    #     """Stores the serialized (pickled) object in memory."""
    #     if input_output not in ['input', 'output']:
    #         raise Exception(f'Invalid input_output: {input_output} it should be "input" or "output"')
    #     try :
    #         if prompt_id not in self.server_data_store :
    #             self.server_data_store[prompt_id] = {}
    #         if server_block_id not in self.server_data_store[prompt_id]:
    #             self.server_data_store[prompt_id][server_block_id] = {'input': [], 'output': []}
    #         if input_output not in self.server_data_store[prompt_id][server_block_id]:
    #             self.server_data_store[prompt_id][server_block_id][input_output] = []
    #         self.server_data_store[prompt_id][server_block_id][input_output].append({'object_id': object_id, 'obj': obj})
    #         return True
    #     except Exception as e:
    #         print(f'Error adding binary object: {e}')
    #         return False

    # def get_binary_server_objects(self, prompt_id, server_block_id, input_output):
    #     """Retrieves all the server objects for the given prompt_id and server_block_id."""
    #     if input_output not in ['input', 'output']:
    #         raise Exception(f'Invalid input_output: {input_output} it should be "input" or "output"')

    #     # print(f'called get_binary_server_objects with prompt_id={prompt_id}, server_block_id={server_block_id}, input_output={input_output}')
    #     if prompt_id is None or server_block_id is None or prompt_id not in self.server_data_store:
    #         return []
    #     return self.server_data_store.get(prompt_id, {}).get(server_block_id, {}).get(input_output, [])
    
    # def set_server_object_status(self, prompt_id, server_block_id, input_output,  status):
    #     if input_output not in ['input', 'output']:
    #         raise Exception(f'Invalid input_output: {input_output} it should be "input" or "output"')

    #     if prompt_id not in self.server_object_status:
    #         self.server_object_status[prompt_id] = {}
    #     if server_block_id not in self.server_object_status[prompt_id]:
    #         self.server_object_status[prompt_id][server_block_id] = {}
    #     if input_output not in self.server_object_status[prompt_id][server_block_id]:
    #         self.server_object_status[prompt_id][server_block_id][input_output] = 'waiting'
    #     self.server_object_status[prompt_id][server_block_id][input_output] = status
    
    # def get_server_object_status(self, prompt_id, server_block_id, input_output):
    #     if input_output not in ['input', 'output']:
    #         raise Exception(f'Invalid input_output: {input_output} it should be "input" or "output"')
    #     if prompt_id not in self.server_object_status or server_block_id not in self.server_object_status[prompt_id] or input_output not in self.server_object_status[prompt_id][server_block_id]:
    #         self.server_object_status[prompt_id][server_block_id][input_output] = 'waiting'
    #         return 'waiting'
    #     return self.server_object_status[prompt_id][server_block_id][input_output]
    
        

    # def add_binary_object(self, prompt_id, object_id, obj, is_input=False):
    #     """Stores the serialized (pickled) object in memory."""
    #     try :
    #         if prompt_id not in self.data_store:
    #             self.data_store[prompt_id] = {'inputs': [], 'outputs': []}
            
    #         key = 'inputs' if is_input else 'outputs'
    #         self.data_store[prompt_id][key].append({'object_id': object_id, 'obj': obj})
    #         return True
    #     except Exception as e:
    #         print(f'Error adding binary object: {e}')
    #         return False

    # def get_binary_object(self, prompt_id, object_id, is_input=False):
    #     """Retrieves and deserializes (unpickles) an object from memory."""
    #     print(f'called get_binary_object with prompt_id={prompt_id}, object_id={object_id}, is_input={is_input}')
    #     if prompt_id is None or object_id is None or prompt_id not in self.data_store:
    #         return None
        
    #     key = 'inputs' if is_input else 'outputs'
    #     for bd in self.data_store[prompt_id][key]:
    #         if bd['object_id'] == object_id:
    #             print(f'object_id={object_id} found in data_store with key={key} obj is {bd["obj"]}')
    #             return bd['obj']
    #     print(f'object_id={object_id} not found in data_store')
    #     return None

    # def get_object_ids(self, prompt_id, is_input=False):
    #     """Retrieves object IDs for the given prompt_id."""
    #     print(f'called get_object_ids with prompt_id={prompt_id}, is_input={is_input}')
    #     if prompt_id is None:
    #         return []
    #     object_ids = []
    #     key = 'inputs' if is_input else 'outputs'
    #     object_ids = [bd['object_id'] for bd in self.data_store.get(prompt_id,{}).get(key,[])]
    #     print(f'returning object_ids={object_ids}')
    #     return object_ids

# New helper functions for execution status management

def update_execution_status(block_data: BlockData, execution_status: ExecutionStatus,
                          current_node: Optional[str] = None, waiting_for: Optional[List[str]] = None,
                          waiting_for_block: Optional[str] = None, error_message: Optional[str] = None) -> None:
    """Update execution status for a block"""
    block_data.execution_info.execution_status = execution_status
    block_data.execution_info.last_heartbeat = time.time()

    if current_node is not None:
        block_data.execution_info.current_node = current_node
    if waiting_for is not None:
        block_data.execution_info.waiting_for = waiting_for
    if waiting_for_block is not None:
        block_data.execution_info.waiting_for_block = waiting_for_block
    if error_message is not None:
        block_data.execution_info.error_message = error_message

def update_client_status(client_data: ClientData, client_status: ClientStatus,
                        current_operation: Optional[str] = None, blocks_pending: Optional[List[str]] = None) -> None:
    """Update client status"""
    client_data.client_info.client_status = client_status
    client_data.client_info.last_heartbeat = time.time()

    if current_operation is not None:
        client_data.client_info.current_operation = current_operation
    if blocks_pending is not None:
        client_data.client_info.blocks_pending = blocks_pending

def get_pending_inputs(block_data: BlockData) -> List[str]:
    """Get list of pending input IDs for a block"""
    pending = []
    for input_id, input_obj in block_data.inputs.items():
        if input_obj.status != Status.READY:
            pending.append(input_id)
    return pending

def is_heartbeat_stale(last_heartbeat: float, max_age_seconds: int = 120) -> bool:
    """Check if heartbeat is stale"""
    return time.time() - last_heartbeat > max_age_seconds

def get_heartbeat_age(last_heartbeat: float) -> float:
    """Get age of heartbeat in seconds"""
    return time.time() - last_heartbeat
