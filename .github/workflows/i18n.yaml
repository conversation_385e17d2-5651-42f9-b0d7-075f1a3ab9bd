name: Update Locales

on:
  pull_request:
    branches: [ main, master, dev* ]

jobs:
  update-locales:
    # Don't run on fork PRs
    if: github.event.pull_request.head.repo.full_name == github.repository
    runs-on: ubuntu-latest
    steps:
    - uses: Comfy-Org/ComfyUI_frontend_setup_action@v2.3
    - name: Install Playwright Browsers
      run: npx playwright install chromium --with-deps
      working-directory: ComfyUI_frontend
    - name: Start dev server
      # Run electron dev server as it is a superset of the web dev server
      # We do want electron specific UIs to be translated.
      run: npm run dev:electron &
      working-directory: ComfyUI_frontend
    - name: Update en.json
      run: npm run collect-i18n -- scripts/collect-i18n-general.ts
      env:
        PLAYWRIGHT_TEST_URL: http://localhost:5173
      working-directory: ComfyUI_frontend
    - name: Update translations
      run: npm run locale
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      working-directory: ComfyUI_frontend
    - name: Commit updated locales
      run: |
        git config --global user.name 'github-actions'
        git config --global user.email '<EMAIL>'
        git fetch origin ${{ github.head_ref }}
        # Stash any local changes before checkout
        git stash -u
        git checkout -B ${{ github.head_ref }} origin/${{ github.head_ref }}
        # Apply the stashed changes if any
        git stash pop || true
        git add src/locales/
        git diff --staged --quiet || git commit -m "Update locales [skip ci]"
        git push origin HEAD:${{ github.head_ref }}
      working-directory: ComfyUI_frontend
