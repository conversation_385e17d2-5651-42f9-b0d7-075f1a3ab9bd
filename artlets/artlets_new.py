import logging
import os
import time
import json
import urllib.request
import urllib.parse
import pickle
import websocket  # type: ignore
import comfy.model_management
import comfy.utils
import server
import tempfile
import aiohttp
import asyncio

from artlets.artlet_models import (
     Status, get_or_create_client, get_or_create_prompt,
    get_or_create_block, add_input_data, add_output_data,
    update_block_status, update_prompt_status,get_client_id_from_prompt_id,
    update_all_output_status, parse_server_address,
)

from artlets.artlet_websocket_proxy import create_artlet_websocket_proxy

# Generate a unique client ID for this ComfyUI instance
# CLIENT_ID = str(uuid.uuid4())

class AnyType(str):
    """A special class that is always equal in not equal comparisons. Credit to pythongosssss"""
    def __eq__(self, _) -> bool:
        return True
    def __ne__(self, __value: object) -> bool:
        return False

any = AnyType("*")


def get_server_url( protocol, host, port, path=""):
    """Construct URL from components"""
    return f"{protocol}://{host}:{port}{path}"

class ArtletServerInputBlock:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "server_block_id": ("STRING",),
            },
            "optional": {
                "input_0": (any, {"default": None}),'input_name_0': ("STRING", {"default": ""}),
                "server_block_name": ("STRING", {"default": ""}),
                "prev_server_block_id": ("STRING", {"default": None, "forceInput": True}),
                "input_1": (any, {"default": None}), "input_name_1": ("STRING", {"default": ""}),
                "input_2": (any, {"default": None}), "input_name_2": ("STRING", {"default": ""}),
                "input_3": (any, {"default": None}), "input_name_3": ("STRING", {"default": ""}),
                "input_4": (any, {"default": None}), "input_name_4": ("STRING", {"default": ""}),
                "input_5": (any, {"default": None}), "input_name_5": ("STRING", {"default": ""}),
                "input_6": (any, {"default": None}), "input_name_6": ("STRING", {"default": ""}),
                "input_7": (any, {"default": None}), "input_name_7": ("STRING", {"default": ""}),
                "input_8": (any, {"default": None}), "input_name_8": ("STRING", {"default": ""}),
                "input_9": (any, {"default": None}), "input_name_9": ("STRING", {"default": ""}),
                "update_int": ("INT", {"default": 0, "min": 0, "max": 0xffffffffffffffff}),
            }
        }
    
    RETURN_TYPES = (AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"),
                   AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"),
                   "LIST","STRING")
    RETURN_NAMES = ('input_0', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 
                    'input_6', 'input_7', 'input_8', 'input_9', 'input_names', 'server_block_id')
    FUNCTION = "process_inputs"
    CATEGORY = "QS/artlets"
    OUTPUT_NODE = True
    
    def process_inputs(self, server_block_id, input_0=None, input_name_0='', server_block_name='', prev_server_block_id=None, 
                       input_1=None, input_name_1='', input_2=None, input_name_2='',
                       input_3=None, input_name_3='', input_4=None, input_name_4='', 
                       input_5=None, input_name_5='',input_6=None, input_name_6=None, 
                       input_7=None, input_name_7=None, input_8=None, input_name_8=None, 
                       input_9=None, input_name_9=None, update_int=0):
        #this is PromptServer instance 
        server_instance = server.PromptServer.instance
        artlet_server_data = server_instance.artlet_data.as_server
        #find if its artlet service
        prompt_id = server_instance.last_prompt_id
        client_id = get_client_id_from_prompt_id(server_instance.artlet_data, prompt_id, as_server=True)
        if client_id is None or client_id not in artlet_server_data:
            return ( input_0, input_1, input_2, input_3, input_4, input_5, input_6, input_7, input_8, input_9, 
                    [input_name_0, input_name_1, input_name_2, input_name_3, input_name_4, input_name_5, input_name_6, input_name_7, input_name_8, input_name_9], 
                    server_block_id
            )
        artlet_service = artlet_server_data[client_id].is_artlet_instance
        if not artlet_service:  #this is not artlet server return inputs 
            return ( input_0, input_1, input_2, input_3, input_4, input_5, input_6, input_7, input_8, input_9, 
                    [input_name_0, input_name_1, input_name_2, input_name_3, input_name_4, input_name_5, input_name_6, input_name_7, input_name_8, input_name_9], 
                    server_block_id
            ) 
        #this is server instance get the inputs from client when inputs are ready
        #wait untill inputs for this block are ready
        prompt_model_data = get_or_create_prompt(artlet_server_data[client_id], prompt_id)
        current_block = get_or_create_block(artlet_server_data[client_id].prompts[prompt_id], server_block_id)
        #wait untill all_input_status is ready for the current_block
        wait_count = 0
        print('waiting for inputs: ')
        pbar = comfy.utils.ProgressBar(100)
        progress = 0
        while current_block.all_input_status != Status.READY:
            pbar.update_absolute(progress % 100, total=100)
            time.sleep(0.1)
            wait_count += 1
            if wait_count % 10 == 0:
                progress += 1
            comfy.model_management.throw_exception_if_processing_interrupted()
            if wait_count % 100 == 0:
                print('.')
             
        # The inputs are ready assign client_inputs to input_0, input_1, input_2, input_3, input_4, input_5, input_6, input_7, input_8, input_9
        client_inputs = [None] * 10
        client_inputs = [v.data for v in current_block.inputs.values()]
        print('got inputs: ', client_inputs)
        return ( client_inputs[0], client_inputs[1], client_inputs[2], client_inputs[3], client_inputs[4], client_inputs[5], client_inputs[6], client_inputs[7], client_inputs[8], client_inputs[9], 
            [input_name_0, input_name_1, input_name_2, input_name_3, input_name_4, input_name_5, input_name_6, input_name_7, input_name_8, input_name_9], 
            server_block_id)
        

class ArtletServerOutputBlock:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "server_block_id": ("STRING",),
                },
            "optional": {
                "server_block_name": ("STRING", {"default": ""}),
                "prev_server_block_id": ("STRING", {"default": None, "forceInput": True}),
                "output_0": (any, {"default": None}),"output_1": (any, {"default": None}),
                "output_2": (any, {"default": None}),"output_3": (any, {"default": None}),
                "output_4": (any, {"default": None}),"output_5": (any, {"default": None}),
                "output_6": (any, {"default": None}),"output_7": (any, {"default": None}),
                "output_8": (any, {"default": None}),"output_9": (any, {"default": None}),
                "output_name_0": ("STRING", {"default": ""}),"output_name_1": ("STRING", {"default": ""}),
                "output_name_2": ("STRING", {"default": ""}),"output_name_3": ("STRING", {"default": ""}),
                "output_name_4": ("STRING", {"default": ""}),"output_name_5": ("STRING", {"default": ""}),
                "output_name_6": ("STRING", {"default": ""}),"output_name_7": ("STRING", {"default": ""}),
                "output_name_8": ("STRING", {"default": ""}),"output_name_9": ("STRING", {"default": ""}),
                 "update_int": ("INT", {"default": 0, "min": 0, "max": 0xffffffffffffffff}),
               
            }
        }
    
    RETURN_TYPES = ("LIST","STRING")
    RETURN_NAMES = ("output_names", "server_block_id")
    FUNCTION = "process_outputs"
    CATEGORY = "QS/artlets"
    OUTPUT_NODE = True
    
    def process_outputs(self, server_block_id, server_block_name='', prev_server_block_id=None, 
                        output_0=None, output_1=None, output_2=None, output_3=None, output_4=None, 
                        output_5=None, output_6=None, output_7=None, output_8=None, output_9=None, 
                        output_name_0='', output_name_1='', output_name_2='', output_name_3='', 
                        output_name_4='', output_name_5='', output_name_6='', output_name_7='', 
                        output_name_8='', output_name_9='', update_int=0):
        print('ArtletServerOutputBlock process_outputs')
        server_instance = server.PromptServer.instance
        prompt_id = server_instance.last_prompt_id
        client_id = get_client_id_from_prompt_id(server_instance.artlet_data, prompt_id, as_server=True)
        if client_id is None or client_id not in server_instance.artlet_data.as_server:
            print('client_id is None or client_id not in server_instance.artlet_data.as_server')
            return ([output_name_0, output_name_1, output_name_2, output_name_3, output_name_4, output_name_5, output_name_6, output_name_7, output_name_8, output_name_9], 
                    server_block_id)
        artlet_service = server_instance.artlet_data.as_server[client_id].is_artlet_instance
        if not artlet_service:  #this is not artlet server return inputs 
            print('not artlet service')
            return ([output_name_0, output_name_1, output_name_2, output_name_3, output_name_4, output_name_5, output_name_6, output_name_7, output_name_8, output_name_9], 
                    server_block_id)
        #this is server instance set the outputs for this block and set the status to ready
        print(f"ArtletServerOutputBlock processing outputs for block {server_block_id} in prompt {prompt_id}")
        # Get or create prompt data
        prompt_data = get_or_create_prompt(server_instance.artlet_data.as_server[client_id], prompt_id)
        block_data = get_or_create_block(prompt_data, server_block_id)
        # set the output data 
        for i in range(10):
            output_key = f"output_{i}"
            output_data = locals()[output_key]
            if output_data is not None:
                add_output_data(block_data, output_key, output_data)
        # set the status to ready
        update_all_output_status(block_data, Status.READY)
        update_block_status(block_data)
        print("set output status to ready for block", server_block_id, 'for prompt', prompt_id)
        return ([output_name_0, output_name_1, output_name_2, output_name_3, output_name_4, output_name_5, output_name_6, output_name_7, output_name_8, output_name_9], 
                    server_block_id)
    

class ArtletClientBlock:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "client_block_id": ("STRING",),
                "input_0": (any, {"default": None}),
                "server_block_id": ("STRING", {"default": None}),
            },
            "optional": {
                "prompt_block_id": ("STRING", {"default": None, "forceInput": True}),
                "api_file": ("STRING", {"default": None}),
                "server_address": ("STRING", {"default": None}),
                "single_block_prompt": ("BOOLEAN", {"default": False}),
                "input_1": (any, {"default": None}),
                "input_2": (any, {"default": None}),
                "input_3": (any, {"default": None}),
                "input_4": (any, {"default": None}),
                "input_5": (any, {"default": None}),
                "input_6": (any, {"default": None}),
                "input_7": (any, {"default": None}),
                "input_8": (any, {"default": None}),
                "input_9": (any, {"default": None}),
                "update_int": ("INT", {"default": 0, "min": 0, "max": 0xffffffffffffffff,
                    "tooltip": "The use seed value to force run artlet on server."}),
            },
            "hidden": {
                "run_nodes_on_server": ("BOOLEAN", {"default": False}),
                "unique_id": "UNIQUE_ID",
            }
        }
    
    RETURN_TYPES = (AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"),
                   AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), "STRING")
    RETURN_NAMES = tuple(f"output_{i}" for i in range(10)) + ("client_block_id",)
    FUNCTION = "artlet_client"
    CATEGORY = "QS/qslets"
    OUTPUT_NODE = True
    
    def artlet_client(self, client_block_id, server_block_id=None, prompt_block_id=None,
                     api_file=None, server_address=None, single_block_prompt=False,
                     input_0=None, input_1=None, input_2=None, input_3=None, input_4=None,
                     input_5=None, input_6=None, input_7=None, input_8=None, input_9=None,
                     update_int=0, run_nodes_on_server=False, unique_id=None):
        
        logging.info(f"ArtletClientBlock started for block {client_block_id}")
        
        client_instance = server.PromptServer.instance
        current_prompt_id = client_instance.last_prompt_id
        client_id = get_client_id_from_prompt_id(client_instance.artlet_data, current_prompt_id, as_server=False)
        if client_id is None or client_id not in client_instance.artlet_data.as_client:
            return (input_0, input_1, input_2, input_3, input_4, input_5, input_6, input_7, input_8, input_9, client_block_id)
        
        # Validate inputs
        if single_block_prompt:
            prompt_block_id = None
            assert api_file is not None, 'api_file is required for single block prompt'
        
        # Resolve api_file path if provided
        if api_file:
            api_file = os.path.join(os.path.dirname(__file__), '../artlet', api_file)
        
        # Get or create client data in as_client section
        client_data = get_or_create_client(client_instance.artlet_data, client_id, as_server=False)
        prompt_data = get_or_create_prompt(client_data, current_prompt_id)
        block_data = get_or_create_block(prompt_data, client_block_id)
        block_data.server_block_id = server_block_id
        
        
        # Store inputs
        inputs = {
            "input_0": input_0, "input_1": input_1, "input_2": input_2, "input_3": input_3, "input_4": input_4,
            "input_5": input_5, "input_6": input_6, "input_7": input_7, "input_8": input_8, "input_9": input_9
        }
        
        for key, value in inputs.items():
            if value is not None:
                add_input_data(block_data, key, value)
        
        # Connect to server and submit prompt if this is the first block
        if prompt_block_id in [None, '']:
            assert api_file is not None and server_address is not None, 'api_file and server_address are required for the first block'
            # This is the first block in the chain, so we need to connect to the server
            server_prompt_id = self.submit_server_prompt(client_instance, client_id, client_data, api_file, server_address)
            prompt_data.server_prompt_id = server_prompt_id
            prompt_data.server_address = server_address

            # Establish websocket connection and start proxying
            self.setup_websocket_proxy(client_instance, client_id, client_data, current_prompt_id, server_prompt_id, server_address, prompt_data, run_nodes_on_server, unique_id)
        else:
            # This is a dependent block, so we need to wait for the server prompt to be ready
            server_prompt_id = prompt_data.server_prompt_id
            if not server_prompt_id:
                raise Exception("No server prompt ID found. Make sure the first block in the chain has been executed.")
        outputs = [None] * 10
        # Send inputs to server
        if server_block_id:
            self.send_inputs_to_server(client_id, client_data, server_address, server_prompt_id, server_block_id, inputs)
            
            # Wait for outputs from server
            outputs = self.wait_for_outputs(client_id, client_data, server_address, server_prompt_id, server_block_id)
            
            # Store outputs
            for i, output in enumerate(outputs):
                if output is not None:
                    add_output_data(block_data, f"output_{i}", output)
            
        # Update block status
        block_data.status = Status.READY
        
        # Update prompt status
        update_prompt_status(prompt_data)
        
        logging.info(f"ArtletClientBlock completed for block {client_block_id}")
        
        return tuple(outputs) + (client_block_id,)

    def setup_websocket_proxy(self, client_instance, client_id, client_data, local_prompt_id, server_prompt_id, server_address, prompt_data, run_nodes_on_server, local_node_id):
        """Setup websocket connection and proxy for Artlet communication"""
        try:
            # Connect to remote websocket
            remote_ws = self.connect_websocket(client_id, client_data, server_address)

            # Log the node ID mapping strategy
            if run_nodes_on_server:
                logging.info(f"run_nodes_on_server=True: Node IDs will NOT be transformed (server nodes will be shown)")
            else:
                logging.info(f"run_nodes_on_server=False: Node IDs will be transformed to local node ID: {local_node_id}")

            # Create the websocket proxy
            proxy = create_artlet_websocket_proxy(
                server_instance=client_instance,
                local_client_id=client_id,
                local_prompt_id=local_prompt_id,
                remote_websocket=remote_ws,
                remote_prompt_id=server_prompt_id,
                remote_client_id=client_id,  # Using same client_id for now
                run_nodes_on_server=run_nodes_on_server,
                local_node_id=local_node_id
            )

            # Store the proxy and websocket in prompt data
            prompt_data.websocket = remote_ws
            prompt_data.websocket_proxy = proxy

            # Start the proxy
            proxy.start_proxying()

            logging.info(f"Websocket proxy established for client {client_id}, local prompt {local_prompt_id}, server prompt {server_prompt_id}")

        except Exception as e:
            logging.error(f"Failed to setup websocket proxy: {e}")
            # Don't fail the entire operation if websocket setup fails
            # The Artlet will still work without real-time updates

    def submit_server_prompt(self, client_instance, client_id, client_data, api_file, server_address):
        """Submit a prompt to the artlet server and return the prompt ID"""
        protocol, host, port = parse_server_address(server_address)
        with open(api_file, 'r') as f:
            api = json.load(f)

        auth_headers = client_data.auth_headers if hasattr(client_data, 'auth_headers') else {}
        
        p = {"prompt": api, "client_id": client_id, 'artlet_service': True}
        data = json.dumps(p).encode('utf-8')
        url = get_server_url(protocol, host, port, "/prompt")
        print('sending prompt to server', url)
        
        # Create a custom SSL context that doesn't verify hostnames for internal domains
        ssl_context = None
        if protocol.lower() == 'https':
            import ssl
            ssl_context = ssl.create_default_context()
            # Check if this is an internal domain where we can skip verification
            if '.onazure.' in host or '.internal.' in host or '.local' in host:
                print(f"Using relaxed SSL verification for internal domain: {host}")
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
        
        req = urllib.request.Request(url, data=data, headers=auth_headers, method="POST")
        
        try:
            # Use the custom SSL context if available
            if ssl_context:
                response = urllib.request.urlopen(req, context=ssl_context)
            else:
                response = urllib.request.urlopen(req)
                
            print('response status', response.status)
            if response.status != 200:
                raise Exception(f"Error submitting prompt to server: {response.status}")
            server_prompt_data = json.loads(response.read())
            server_prompt_id = server_prompt_data['prompt_id']
            logging.info(f"Submitted prompt to server, got prompt ID: {server_prompt_id}")
            
            # Store the SSL context in client_data for future requests
            if ssl_context:
                client_data.ssl_context = ssl_context
                
            return server_prompt_id
        except Exception as e:
            logging.error(f"Failed to submit prompt to server: {e}")
            raise e    
        
    def connect_websocket(self, client_id, client_data, server_address):
        """Connect to the server's websocket"""
        protocol, host, port = parse_server_address(server_address)
        ws_protocol = "ws" if protocol == "http" else "wss"
        # Base WebSocket URL
        ws_url = f"{ws_protocol}://{host}:{port}/ws?clientId={client_id}"

        # Get authentication headers following the same pattern as submit_server_prompt
        auth_headers = client_data.auth_headers if hasattr(client_data, 'auth_headers') else {}

        # Get or create SSL context following the same pattern as submit_server_prompt
        ssl_context = getattr(client_data, 'ssl_context', None)
        if ssl_context is None and protocol.lower() == 'https':
            import ssl
            ssl_context = ssl.create_default_context()
            # Check if this is an internal domain where we can skip verification
            if host and ('.onazure.' in host or '.internal.' in host or '.local' in host):
                print(f"Using relaxed SSL verification for internal domain: {host}")
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

        # Create WebSocket connection with headers and SSL context
        try:
            # Convert headers to list of tuples format expected by websocket library
            header_list = []
            if auth_headers:
                for k, v in auth_headers.items():
                    if k is not None and v is not None:
                        header_list.append(f"{k}: {v}")
                logging.info(f"Connecting to websocket with headers: {list(auth_headers.keys())}")
            else:
                logging.info("Connecting to websocket without headers")

            # Create websocket connection with SSL context if needed
            if ssl_context:
                ws = websocket.create_connection(ws_url, header=header_list, sslopt={"context": ssl_context})
            else:
                ws = websocket.create_connection(ws_url, header=header_list)

            logging.info(f"Connected to websocket at {ws_url}")
            return ws
        except Exception as e:
            logging.error(f"Failed to connect to websocket {ws_url}: {e}")
            raise e
        
    def send_inputs_to_server(self, client_id, client_data, server_address, server_prompt_id, server_block_id, inputs):
        """Send input data to the server using file upload for large data"""
        protocol, host, port = parse_server_address(server_address)
        
        # Add authentication headers if available
        headers = {}
        if hasattr(client_data, 'auth_headers') and client_data.auth_headers:
            for header, value in client_data.auth_headers.items():
                if header != "Content-Type":  # Don't override content type
                    headers[header] = value
        
        # Get SSL context if available
        ssl_context = getattr(client_data, 'ssl_context', None)
        
        success = True
        for i, (key, input_data) in enumerate(inputs.items()):
            if input_data is None:
                continue
            
            object_id = f"input_{i}"
            
            # Serialize the data
            bytecode_data = pickle.dumps(input_data)
            data_size = len(bytecode_data)
            
            # For large data, use file upload approach
            if data_size > 10 * 1024: # * 1024:  # 10MB threshold
                # Create a temporary file
                print('using temp file method')
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pickle') as temp_file:
                    temp_file.write(bytecode_data)
                    temp_path = temp_file.name
                
                try:
                    # Use multipart form data to upload the file
                    url = f"{protocol}://{host}:{port}/artlet/object_file/{server_prompt_id}/{server_block_id}/input/{object_id}"
                    
                    # Create form data with file
                    form = aiohttp.FormData()
                    form.add_field('file', 
                                open(temp_path, 'rb'),
                                filename=f'{object_id}.pickle',
                                content_type='application/octet-stream')
                    
                    # Since we're using aiohttp for multipart, we need to run this in an async context
                    # We'll use a synchronous wrapper around the async code
                    async def upload_file():
                        async with aiohttp.ClientSession() as session:
                            async with session.post(url, data=form, headers=headers, ssl=ssl_context) as response:
                                if response.status != 200:
                                    text = await response.text()
                                    raise Exception(f"Error uploading file: {response.status}, {text}")
                                return await response.text()
                    
                    # Run the async function in a new event loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(upload_file())
                    finally:
                        loop.close()
                        
                    # Clean up the temporary file
                    os.unlink(temp_path)
                    
                except Exception as e:
                    logging.error(f"Failed to upload file for input {i}: {e}")
                    success = False
                    # Clean up temp file if it still exists
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
            else:
                # For small data, use the original method
                url = f"{protocol}://{host}:{port}/artlet/object/{server_prompt_id}/{server_block_id}/input/{object_id}"
                
                # Create and send the request with authentication headers
                req = urllib.request.Request(url, data=bytecode_data, method="POST")
                for header, value in headers.items():
                    req.add_header(header, value)
                
                try:
                    if ssl_context:
                        response = urllib.request.urlopen(req, context=ssl_context)
                    else:
                        response = urllib.request.urlopen(req)
                        
                    if response.status != 200:
                        logging.error(f"Error sending input {i} to server: {response.status}")
                        success = False
                except Exception as e:
                    logging.error(f"Failed to send input {i} to server: {e}")
                    success = False
        
        # Send status that all inputs are ready
        if success:
            print('sending input status')
            url = f"{protocol}://{host}:{port}/artlet/object_status/{server_prompt_id}/{server_block_id}/input"
            data = json.dumps({"status": "ready"}).encode("utf-8")
            req = urllib.request.Request(url, data=data, method="POST")
            for header, value in headers.items():
                req.add_header(header, value)
            
            try:
                if ssl_context:
                    response = urllib.request.urlopen(req, context=ssl_context)
                else:
                    response = urllib.request.urlopen(req)
                    
                if response.status != 200:
                    logging.error(f"Error sending input status to server: {response.status}")
                    success = False
            except Exception as e:
                logging.error(f"Failed to send input status to server: {e}")
                success = False
                
        return success


    def wait_for_outputs(self, client_id, client_data, server_address, server_prompt_id, server_block_id):
        """Wait for outputs from the server"""
        protocol, host, port = parse_server_address(server_address)
        outputs = [None] * 10
        
        # Poll for output status
        max_retries = 100
        retry_delay = 0.5
        retry = 0
        pbar = comfy.utils.ProgressBar(100)
        progress = 0
        
        # Ensure we have auth headers
        auth_headers = client_data.auth_headers if hasattr(client_data, 'auth_headers') else {}
        
        # Get SSL context if available
        ssl_context = getattr(client_data, 'ssl_context', None)
        
        while True:
            # Check if processing is interrupted
            print('waiting for outputs: ')
            comfy.model_management.throw_exception_if_processing_interrupted()
            pbar.update_absolute(progress % 100, total=100)
            
            # Check output status
            url = f"{protocol}://{host}:{port}/artlet/object_status/{server_prompt_id}/{server_block_id}/output"
            print('checking output status url', url)
            
            # Create request with auth headers
            req = urllib.request.Request(url, headers=auth_headers, method="GET")
            
            try:
                if ssl_context:
                    response = urllib.request.urlopen(req, context=ssl_context)
                else:
                    response = urllib.request.urlopen(req)
                    
                print('response status', response.status)
                
                if response.status != 200:
                    raise Exception(f"Error checking output status: {response.status}")

                result = response.read()
                
                # Try to parse the response - handle both string and dict formats
                try:
                    # First try to parse as JSON string
                    result_str = result.decode('utf-8') if isinstance(result, bytes) else result
                    status_data = json.loads(result_str)
                    
                    # Check if we need to parse again (sometimes servers double-encode)
                    if isinstance(status_data, str):
                        status = json.loads(status_data)
                    else:
                        status = status_data
                        
                    print('output status', status, 'for server_prompt_id', server_prompt_id, 'server_block_id', server_block_id)
                    
                    if status.get("status") == "ready":
                        # Fetch all outputs
                        for i in range(10):
                            object_id = f"output_{i}"
                            url = f"{protocol}://{host}:{port}/artlet/object/{server_prompt_id}/{server_block_id}/output/{object_id}"
                            req = urllib.request.Request(url, headers=auth_headers, method="GET")
                            
                            try:
                                if ssl_context:
                                    output_response = urllib.request.urlopen(req, context=ssl_context)
                                else:
                                    output_response = urllib.request.urlopen(req)
                                    
                                if output_response.status == 200:
                                    result = output_response.read()
                                    try:
                                        output_data = pickle.loads(result)
                                        outputs[i] = output_data
                                        logging.info(f"Received output_{i} from server")
                                    except Exception as e:
                                        logging.warning(f"Failed to load output_{i}: {e}")
                            except Exception as e:
                                logging.warning(f"Failed to fetch output_{i}: {e}")
                                
                        return outputs
                    
                except json.JSONDecodeError as e:
                    # If we get HTML instead of JSON, it's likely an auth issue
                    if b"<html" in result or b"<!DOCTYPE" in result:
                        logging.error(f"Authentication error: Received HTML instead of JSON. Check your auth credentials.")
                        raise Exception("Authentication failed: Received login page instead of data")
                    else:
                        logging.error(f"JSON decode error: {e}, Response: {result[:100]}...")
                        
            except Exception as e:
                logging.error(f"Error checking output status: {e}")
                # Continue retrying despite errors
                #since it is an exception from server raise an exception so that it reaches client
                raise e
                
            
            # Not ready yet, wait and retry
            retry += 1
            progress += 1
            
            # if retry >= max_retries:
            #     logging.warning(f"Max retries ({max_retries}) reached waiting for outputs")
            #     break
                
            time.sleep(retry_delay)
        
        # If we get here, we've timed out
        # raise Exception(f"Timed out waiting for outputs after {max_retries} retries")
    
            
            
class ArtletDescription:
    def __init__(self):
        pass
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "api_file": ("STRING",),
            },
        }
    
    RETURN_TYPES = ("STRING",)
    RETURN_NAMES = ("description",)
    FUNCTION = "artlet_description"
    CATEGORY = "Artlet"
    OUTPUT_NODE = True
    
    def artlet_description(self, api_file):
        print(f"Starting artlet_description with api_file: {api_file}")
        
        # Check if file exists and read it
        if not os.path.exists(api_file):
            api_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'artlet', api_file)
        
        if not os.path.exists(api_file):
            print(f"File not found: {api_file}")
            return (json.dumps({"error": f"File not found: {api_file}"}),)
        
        try:
            with open(api_file, 'r') as f:
                api_data = json.load(f)
            
            print(f"File loaded successfully. Found {len(api_data)} nodes")
            
            # First pass: collect all blocks by server_block_id
            blocks_by_id = {}
            input_blocks = 0
            output_blocks = 0
            
            for node_id, node_data in api_data.items():
                class_type = node_data.get('class_type', '')
                
                if class_type in ['ArtletServerInputBlock', 'ArtletServerOutputBlock']:
                    inputs = node_data.get('inputs', {})
                    server_block_id = inputs.get('server_block_id', '')
                    
                    if not server_block_id:
                        continue
                    
                    if server_block_id not in blocks_by_id:
                        blocks_by_id[server_block_id] = {
                            'id': server_block_id,
                            'server_block_name': inputs.get('server_block_name', ''),
                            'prev_server_block_id': inputs.get('prev_server_block_id', '')
                        }
                    
                    # Process inputs for ArtletServerInputBlock
                    if class_type == 'ArtletServerInputBlock':
                        input_blocks += 1
                        
                        block_inputs = []
                        for i in range(10):
                            input_name_key = f'input_name_{i}'
                            input_key = f'input_{i}'
                            
                            input_name = inputs.get(input_name_key, '')
                            input_value = inputs.get(input_key, None)
                            
                            # Skip only if both name is empty AND value is None
                            if input_name == '' and input_value is None:
                                continue
                                
                            block_inputs.append({
                                input_name_key: input_name,
                                input_key: 'yes' if input_value is not None else 'no'
                            })
                        
                        blocks_by_id[server_block_id]['inputs'] = block_inputs

                    # Process outputs for ArtletServerOutputBlock
                    if class_type == 'ArtletServerOutputBlock':
                        output_blocks += 1
                        
                        block_outputs = []
                        for i in range(10):
                            output_name_key = f'output_name_{i}'
                            output_key = f'output_{i}'
                            
                            output_name = inputs.get(output_name_key, '')
                            output_value = inputs.get(output_key, None)
                            
                            
                            # Skip only if both name is empty AND value is None
                            if output_name == '' and output_value is None:
                                continue
                                
                            block_outputs.append({
                                output_name_key: output_name,
                                output_key: 'yes' if output_value is not None else 'no'
                            })
                        
                        blocks_by_id[server_block_id]['outputs'] = block_outputs
            
            print(f"Found {input_blocks} input blocks and {output_blocks} output blocks")
            
            # Convert to list and ensure all blocks have inputs and outputs fields
            server_blocks = []
            for block_id, block_data in blocks_by_id.items():
                if 'inputs' not in block_data:
                    block_data['inputs'] = []
                if 'outputs' not in block_data:
                    block_data['outputs'] = []
                server_blocks.append(block_data)
            
            # Create the final description
            description = {
                'file': os.path.basename(api_file),
                'server_blocks': server_blocks
            }
            
            api_description = json.dumps(description, indent=2)
            print(f"Returning description with {len(server_blocks)} server blocks")
            return (api_description,)
        except Exception as e:
            import traceback
            error_msg = traceback.format_exc()
            print(f"Error processing file: {str(e)}")
            print(error_msg)
            artlet_description = json.dumps({
                "error": f"Failed to parse API file: {str(e)}",
                "traceback": error_msg
            })
            return (artlet_description,)


class ArtletPromptGenerator:
    def __init__(self):
        pass
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "server_node_list": ("STRING",),
                "sequence_id": ("INT",{"default": 0, "min": 0, "max": 0xffffffffffffffff}),
                "run_nodes_on_server": ("BOOLEAN", {"default": False}),
                "save_prompts": ("BOOLEAN", {"default": False}),
            },
            "optional": {
                "client_block_id": ("STRING", {"default": "dummy_client_id"}),
                "server_block_id": ("STRING", {"default": "dummy_server_id"}),
                "server_address": ("STRING",{"default": "http://localhost:18891"}),
                "single_prompt_block": ("BOOLEAN", {"default": True}),
                "server_api_file": ("STRING", {"default": "server/server_prompt.json"}),
                "client_api_file": ("STRING", {"default": "client/client_prompt.json"}),
                "update_int":("INT", {"default": 0, "min": 0, "max": 0xffffffffffffffff}),
            }
        }
    
    RETURN_TYPES = ()
    RETURN_NAMES = ()
    FUNCTION = "artlet_prompt_generator"
    CATEGORY = "Artlet"
    OUTPUT_NODE = True

    def artlet_prompt_generator(self, server_node_list, sequence_id,  run_nodes_on_server, save_prompts, client_block_id="dummy_client_id", 
                                server_block_id="dummy_server_id", server_address="http://localhost:18891", 
                                single_prompt_block=True, server_api_file="server/server_prompt.json", 
                                client_api_file="client/client_prompt.json", update_int=0): 
        print("This is placehoder node for passing input to server running with inputs", locals())

        return ()

    

    # def cleanup_server_prompt(self, client_instance, server_address, server_prompt_id):
    #     """Clean up server resources for a prompt"""
    #     protocol, host, port = parse_server_address(server_address)
    #     try:
    #         url = f"{protocol}://{host}:{port}/artlet/prompt_remove/{server_prompt_id}/"
    #         req = urllib.request.Request(url, method="POST")
    #         result = urllib.request.urlopen(req).read()
    #         logging.info(f"Cleaned up server prompt {server_prompt_id}, result: {result}")
    #     except Exception as e:
    #         logging.warning(f"Failed to clean up server prompt: {e}")
