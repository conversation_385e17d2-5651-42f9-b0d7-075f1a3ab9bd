{"last_node_id": 3, "last_link_id": 1, "nodes": [{"id": 1, "type": "PrimitiveNode", "pos": [14, 43], "size": [203.1999969482422, 40.36840057373047], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "connect to widget input", "type": "*", "links": [], "slot_index": 0}], "properties": {"Run widget replace on values": false}}, {"id": 2, "type": "CLIPTextEncode", "pos": [306.2463684082031, 45.30042266845703], "size": [400, 200], "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": null}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}], "links": [], "groups": [], "config": {}, "extra": {"ds": {"offset": [0, 0], "scale": 1}}, "version": 0.4}