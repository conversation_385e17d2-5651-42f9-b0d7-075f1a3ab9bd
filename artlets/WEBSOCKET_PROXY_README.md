# Artlet Websocket Proxy System

## Overview

The Artlet Websocket Proxy system enables real-time communication between ComfyUI-frontend and remote Artlet servers. When an `ArtletClientBlock` submits a prompt to a remote server, it establishes a websocket connection to that server and proxies all websocket messages back to the local ComfyUI-frontend.

## Architecture

```
ComfyUI-Frontend ←→ Local qsComfy Server ←→ ArtletWebsocketProxy ←→ Remote Artlet Server
```

### Key Components

1. **ArtletMessageTransformer**: Transforms websocket messages from remote server format to local frontend format
2. **ArtletWebsocketProxy**: Manages the websocket connection and message proxying
3. **send_artlet_proxy_sync**: Server method for sending transformed messages to local frontend
4. **Websocket connection management**: Automatic setup and cleanup

## Message Transformation

The system transforms the following message types:

### JSON Messages
- `executing`: Node execution start
- `executed`: Node execution completion
- `progress`: Progress updates during execution
- `execution_start`: Prompt execution start
- `execution_cached`: Cached node information
- `execution_success`: Successful execution completion
- `execution_error`: Execution errors
- `execution_interrupted`: Execution interruption

### Binary Messages
- **Structured Binary Messages**: Messages with JSON metadata + binary data (e.g., preview images)
- **Raw Binary Messages**: Pure binary data without metadata
- **Binary Event Types**:
  - `ARTLET_BINARY_MESSAGE` (1000): Structured binary messages from Artlet servers
  - `ARTLET_BINARY_DATA` (1001): Raw binary data from Artlet servers

### ID Mapping

Messages are transformed to map remote IDs to local IDs:

- **prompt_id**: `remote_prompt_id` → `local_prompt_id`
- **node**: `remote_node_id` → `local_node_id` 
- **display_node**: `remote_display_node` → `local_display_node`
- **nodes** (arrays): Each remote node ID mapped to local equivalent
- **executed** (arrays): Each remote node ID mapped to local equivalent

## Implementation Details

### ArtletClientBlock Integration

When the first `ArtletClientBlock` in a prompt chain executes:

1. Submits prompt to remote server
2. Calls `setup_websocket_proxy()` to establish connection
3. Creates `ArtletWebsocketProxy` instance
4. Starts background thread for message proxying
5. Stores proxy reference in `prompt_data.websocket_proxy`

### Message Flow

#### JSON Messages
1. Remote server sends JSON websocket message
2. `ArtletWebsocketProxy` receives message in background thread
3. `ArtletMessageTransformer` transforms message data (IDs, etc.)
4. `send_artlet_proxy_sync()` forwards transformed JSON to local frontend
5. ComfyUI-frontend receives message as if from local server

#### Binary Messages
1. Remote server sends binary websocket message
2. `ArtletWebsocketProxy` receives binary message in background thread
3. **Structured Binary**: Parse JSON metadata, transform IDs, reconstruct with original binary data
4. **Raw Binary**: Forward as-is (no ID transformation needed)
5. `send_artlet_proxy_sync()` forwards binary message with appropriate event type
6. ComfyUI-frontend receives binary message (e.g., preview images, progress data)

### Cleanup

When prompt execution completes or fails:

1. `cleanup_artlet_client_instance()` is called
2. Websocket proxy is stopped via `proxy.stop_proxying()`
3. Remote websocket connection is closed
4. All references are cleaned up

## Configuration

### Authentication Headers

Authentication headers are automatically extracted from the initial websocket connection and used for the remote websocket connection:

- `X-Auth-Token`
- `X-Auth-User` 
- `X-Auth-Email`
- `Cookie`

### Node ID Mapping

Currently uses a simple mapping approach. For more sophisticated scenarios, the mapping can be built from the API file to provide accurate node ID translation.

## Error Handling

- Websocket connection failures are logged but don't fail the entire Artlet operation
- Message parsing errors are logged and skipped
- Proxy thread exceptions are caught and logged
- Cleanup is performed even if errors occur

## Benefits

1. **Real-time Updates**: Frontend receives progress and status updates from remote execution
2. **Transparent Operation**: Frontend behaves as if execution is local
3. **Clean Separation**: Artlet-specific logic is isolated from core websocket system
4. **Robust Cleanup**: Proper resource management prevents memory leaks
5. **Authentication Support**: Seamless forwarding of authentication credentials

## Future Enhancements

1. **Binary Message Support**: Currently only JSON messages are proxied
2. **Advanced Node Mapping**: Build mapping from API file analysis
3. **Message Filtering**: Allow filtering of specific message types
4. **Reconnection Logic**: Automatic reconnection on connection loss
5. **Performance Optimization**: Message batching and compression

## Usage Example

```python
# In ArtletClientBlock
def setup_websocket_proxy(self, client_instance, client_id, client_data, 
                         local_prompt_id, server_prompt_id, server_address, prompt_data):
    # Connect to remote websocket
    remote_ws = self.connect_websocket(client_id, client_data, server_address)
    
    # Create proxy with ID mapping
    proxy = create_artlet_websocket_proxy(
        server_instance=client_instance,
        local_client_id=client_id,
        local_prompt_id=local_prompt_id,
        remote_websocket=remote_ws,
        remote_prompt_id=server_prompt_id,
        remote_client_id=client_id,
        node_id_mapping={},  # Built from API analysis
        send_proxy_func=client_instance.send_artlet_proxy_sync
    )
    
    # Store and start proxy
    prompt_data.websocket_proxy = proxy
    proxy.start_proxying()
```

This system provides seamless real-time communication for distributed Artlet execution while maintaining clean separation from the core ComfyUI websocket infrastructure.
