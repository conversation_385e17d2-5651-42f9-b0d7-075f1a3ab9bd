#!/usr/bin/env python3
"""
Test runner for artlet deadlock detection functionality
"""

import sys
import os
import subprocess
import argparse

# Add qsComfy root directory to path
qscomfy_root = os.getcwd()
sys.path.insert(0, qscomfy_root)

def run_unit_tests():
    """Run the unit tests"""
    print("Running unit tests...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_artlet_deadlock_detection.py", 
            "-v"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except FileNotFoundError:
        print("pytest not found, running with unittest...")
        try:
            result = subprocess.run([
                sys.executable, 
                "tests/test_artlet_deadlock_detection.py"
            ], capture_output=True, text=True)
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            return result.returncode == 0
        except Exception as e:
            print(f"Error running unit tests: {e}")
            return False

def run_integration_tests():
    """Run the integration tests"""
    print("Running integration tests...")
    try:
        result = subprocess.run([
            sys.executable, 
            "tests/test_artlet_integration.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running integration tests: {e}")
        return False

def check_imports():
    """Check if all required modules can be imported"""
    print("Checking imports...")
    
    try:
        from artlets.artlet_models import ExecutionStatus, ClientStatus
        print("✓ artlet_models imports working")
    except ImportError as e:
        print(f"✗ artlet_models import error: {e}")
        return False
    
    try:
        from artlets.artlet_exceptions import DeadlockException
        print("✓ artlet_exceptions imports working")
    except ImportError as e:
        print(f"✗ artlet_exceptions import error: {e}")
        return False
    
    try:
        # Check if the new endpoints are available
        import artlets.artlet_helpers
        print("✓ artlet_helpers imports working")
    except ImportError as e:
        print(f"✗ artlet_helpers import error: {e}")
        return False
    
    return True

def check_server_status(server_url="http://localhost:8188"):
    """Check if qsComfy server is running"""
    print(f"Checking server status at {server_url}...")
    
    try:
        import requests
        response = requests.get(f"{server_url}/system_stats", timeout=5)
        if response.status_code == 200:
            print("✓ qsComfy server is running")
            return True
        else:
            print(f"✗ Server responded with status {response.status_code}")
            return False
    except ImportError:
        print("⚠ requests module not available, skipping server check")
        return False
    except Exception as e:
        print(f"✗ Cannot connect to server: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Test artlet deadlock detection")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--server-url", default="http://localhost:8188", help="Server URL for testing")
    
    args = parser.parse_args()
    
    print("ARTLET DEADLOCK DETECTION - TEST RUNNER")
    print("=" * 50)
    
    # Always check imports first
    if not check_imports():
        print("\n✗ Import checks failed. Please fix import errors before running tests.")
        return 1
    
    success = True
    
    # Check server status
    server_running = check_server_status(args.server_url)
    
    if args.unit or (not args.unit and not args.integration):
        print("\n" + "-" * 30)
        if not run_unit_tests():
            success = False
    
    if args.integration or (not args.unit and not args.integration):
        print("\n" + "-" * 30)
        if not run_integration_tests():
            success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✓ ALL TESTS PASSED")
        if server_running:
            print("✓ Server endpoints tested successfully")
        else:
            print("⚠ Server not running - endpoint tests skipped")
    else:
        print("✗ SOME TESTS FAILED")
    
    print("\nTo test with a running server:")
    print(f"1. Start qsComfy server: python main.py")
    print(f"2. Run tests: python run_artlet_tests.py")
    print("\nTo test specific components:")
    print("- Unit tests only: python run_artlet_tests.py --unit")
    print("- Integration tests only: python run_artlet_tests.py --integration")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
