name: Version Bump

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version increment type'
        required: true
        default: 'patch'
        type: 'choice'
        options:
          - patch
          - minor
          - major

jobs:
  bump-version:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'npm'

      - name: Bump version
        id: bump-version
        run: |
          npm version ${{ github.event.inputs.version_type }} --no-git-tag-version
          NEW_VERSION=$(node -p "require('./package.json').version")
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_OUTPUT

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.PR_GH_TOKEN }}
          commit-message: '[release] Bump version to ${{ steps.bump-version.outputs.NEW_VERSION }}'
          title: '${{ steps.bump-version.outputs.NEW_VERSION }}'
          body: |
            Automated version bump to ${{ steps.bump-version.outputs.NEW_VERSION }}
          branch: version-bump-${{ steps.bump-version.outputs.NEW_VERSION }}
          base: main
          labels: |
            Release