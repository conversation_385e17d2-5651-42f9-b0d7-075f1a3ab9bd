#!/usr/bin/env python3
"""
Manual test script for artlet server endpoints
Run this while qsComfy server is running: python test_server_endpoints.py
"""

import sys
import os
import time

# Add qsComfy root directory to path
sys.path.insert(0, os.getcwd())

def test_with_requests():
    """Test endpoints using requests library"""
    try:
        import requests
    except ImportError:
        print("❌ requests library not available")
        return False
    
    server_url = "http://localhost:8188"
    print(f"Testing server endpoints at {server_url}")
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f"{server_url}/system_stats", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False
    
    # Test 2: Test new artlet endpoints
    endpoints_to_test = [
        ("/artlet/execution_status/test_prompt/test_block", "GET"),
        ("/artlet/client_status/test_client/test_prompt", "GET"),
        ("/artlet/prompt_status/test_prompt", "GET"),
        ("/artlet/client_heartbeat/test_client", "POST")
    ]
    
    for endpoint, method in endpoints_to_test:
        try:
            if method == "GET":
                response = requests.get(f"{server_url}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{server_url}{endpoint}", timeout=5)
            
            print(f"✅ {method} {endpoint} -> {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Response: {data}")
                except:
                    print(f"   Response: {response.text[:100]}...")
            
        except Exception as e:
            print(f"❌ {method} {endpoint} -> Error: {e}")
    
    return True

def test_with_urllib():
    """Test endpoints using urllib (no external dependencies)"""
    import urllib.request
    import json
    
    server_url = "http://localhost:8188"
    print(f"Testing server endpoints at {server_url} (using urllib)")
    
    # Test 1: Check if server is running
    try:
        response = urllib.request.urlopen(f"{server_url}/system_stats", timeout=5)
        if response.status == 200:
            print("✅ Server is running and accessible")
        else:
            print(f"❌ Server responded with status {response.status}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False
    
    # Test 2: Test new artlet endpoints
    endpoints_to_test = [
        ("/artlet/execution_status/test_prompt/test_block", "GET"),
        ("/artlet/client_status/test_client/test_prompt", "GET"),
        ("/artlet/prompt_status/test_prompt", "GET"),
    ]
    
    for endpoint, method in endpoints_to_test:
        try:
            if method == "GET":
                response = urllib.request.urlopen(f"{server_url}{endpoint}", timeout=5)
            
            print(f"✅ {method} {endpoint} -> {response.status}")
            if response.status == 200:
                try:
                    data = response.read().decode('utf-8')
                    parsed = json.loads(data)
                    print(f"   Response: {parsed}")
                except:
                    print(f"   Response: {data[:100]}...")
            
        except Exception as e:
            print(f"❌ {method} {endpoint} -> Error: {e}")
    
    # Test POST endpoint
    try:
        req = urllib.request.Request(f"{server_url}/artlet/client_heartbeat/test_client", method="POST")
        response = urllib.request.urlopen(req, timeout=5)
        print(f"✅ POST /artlet/client_heartbeat/test_client -> {response.status}")
    except Exception as e:
        print(f"❌ POST /artlet/client_heartbeat/test_client -> Error: {e}")
    
    return True

def test_data_structures():
    """Test the new data structures work correctly"""
    print("\nTesting data structures...")
    
    try:
        from artlets.artlet_models import (
            ExecutionStatus, ClientStatus, BlockData, ClientData,
            update_execution_status, update_client_status, get_heartbeat_age
        )
        
        # Test BlockData creation
        block = BlockData(block_id="test_block")
        print(f"✅ BlockData created: {block.block_id}")
        print(f"✅ Initial execution status: {block.execution_info.execution_status}")
        
        # Test status updates
        update_execution_status(block, ExecutionStatus.EXECUTING, current_node="test_node")
        print(f"✅ Updated execution status: {block.execution_info.execution_status}")
        print(f"✅ Current node: {block.execution_info.current_node}")
        
        # Test client data
        client = ClientData(client_id="test_client")
        update_client_status(client, ClientStatus.SENDING_DATA, current_operation="test_op")
        print(f"✅ Client status: {client.client_info.client_status}")
        print(f"✅ Current operation: {client.client_info.current_operation}")
        
        # Test heartbeat
        heartbeat_age = get_heartbeat_age(time.time() - 5)
        print(f"✅ Heartbeat age calculation: {heartbeat_age:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Data structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("ARTLET DEADLOCK DETECTION - MANUAL SERVER TEST")
    print("=" * 60)
    
    # Test data structures first (always works)
    data_ok = test_data_structures()
    
    print("\n" + "=" * 60)
    print("TESTING SERVER ENDPOINTS")
    print("=" * 60)
    
    # Try requests first, fall back to urllib
    server_ok = test_with_requests()
    if not server_ok:
        print("\nFalling back to urllib...")
        server_ok = test_with_urllib()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if data_ok:
        print("✅ Data structures working correctly")
    else:
        print("❌ Data structure issues found")
    
    if server_ok:
        print("✅ Server endpoints accessible and responding")
        print("🎉 Ready for full testing with artlet workflows!")
    else:
        print("❌ Server not accessible")
        print("💡 Start the server with: python main.py")
    
    print("\nNext steps:")
    print("1. If server tests passed, try creating artlet workflows")
    print("2. Test smart waiting with long-running operations")
    print("3. Test client-server disconnection scenarios")

if __name__ == "__main__":
    main()
