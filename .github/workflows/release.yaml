name: Create Release Draft

on:
  pull_request:
    types: [ closed ]
    branches: [ main, core/* ]
    paths:
      - 'package.json'

jobs:
  build:
    runs-on: ubuntu-latest
    if: >
      github.event.pull_request.merged == true &&
      contains(github.event.pull_request.labels.*.name, 'Release')
    outputs:
      version: ${{ steps.current_version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
      - name: Get current version
        id: current_version
        run: echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
      - name: Build project
        env:
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          ALGOLIA_APP_ID: ${{ secrets.ALGOLIA_APP_ID }}
          ALGOLIA_API_KEY: ${{ secrets.ALGOLIA_API_KEY }}
          USE_PROD_CONFIG: 'true'
        run: |
          npm ci
          npm run build
          npm run zipdist
      - name: Upload dist artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist-files
          path: |
            dist/
            dist.zip

  draft_release:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Download dist artifact
        uses: actions/download-artifact@v4
        with:
          name: dist-files
      - name: Create release
        id: create_release
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          files: |
            dist.zip
          tag_name: v${{ needs.build.outputs.version }}
          target_commitish: ${{ github.event.pull_request.base.ref }}
          make_latest: ${{ github.event.pull_request.base.ref == 'main' }}
          draft: ${{ github.event.pull_request.base.ref != 'main' }}
          prerelease: false
          generate_release_notes: true

  publish_pypi:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Download dist artifact
        uses: actions/download-artifact@v4
        with:
          name: dist-files
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
      - name: Install build dependencies
        run: python -m pip install build
      - name: Setup pypi package
        run: |
          mkdir -p comfyui_frontend_package/comfyui_frontend_package/static/
          cp -r dist/* comfyui_frontend_package/comfyui_frontend_package/static/
      - name: Build pypi package
        run: python -m build
        working-directory: comfyui_frontend_package
        env:
          COMFYUI_FRONTEND_VERSION: ${{ needs.build.outputs.version }}
      - name: Publish pypi package
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_TOKEN }}
          packages-dir: comfyui_frontend_package/dist

  publish_types:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          registry-url: https://registry.npmjs.org
      - run: npm ci
      - run: npm run build:types
      - name: Publish package
        run: npm publish --access public
        working-directory: dist
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
