{"last_node_id": 2, "last_link_id": 1, "nodes": [{"id": 2, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 304.3653259277344, "1": 42.15586471557617}, "size": [315, 262], "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null}, {"name": "positive", "type": "CONDITIONING", "link": null}, {"name": "negative", "type": "CONDITIONING", "link": null}, {"name": "latent_image", "type": "LATENT", "link": null}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [0, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 1, "type": "PrimitiveNode", "pos": {"0": 14, "1": 43}, "size": [203.1999969482422, 40.368401303242536], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "connect to widget input", "type": "*", "links": [], "slot_index": 0}], "properties": {"Run widget replace on values": false}, "widgets_values": []}], "links": [], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [0, 0]}}, "version": 0.4}