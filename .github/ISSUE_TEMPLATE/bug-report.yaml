name: Bug Report
description: "Something is not behaving as expected."
title: "[Bug]: "
labels: ['Potential Bug']
body:
  - type: markdown
    attributes:
      value: |
        Before submitting a **Bug Report**, please ensure the following:

        - **1:** You are running the latest version of ComfyUI.
        - **2:** You have looked at the existing bug reports and made sure this isn't already reported.
        - **3:** You confirmed that the bug is not caused by a custom node. You can disable all custom nodes by passing
        `--disable-all-custom-nodes` command line argument.

  - type: textarea
    attributes:
      label: Frontend Version
      description: |
        What is the frontend version you are using? You can check this in the settings dialog.

        <details>

        <summary>Click to show where to find the version</summary>

        Open the setting by clicking the cog icon in the bottom-left of the screen, then click `About`.

        ![Frontend version](https://github.com/user-attachments/assets/561fb7c3-3012-457c-a494-9bdc1ff035c0)

        </details>
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: 'What you expected to happen.'
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual Behavior
      description: 'What actually happened. Please include a screenshot / video clip of the issue if possible.'
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: "Describe how to reproduce the issue. Please be sure to attach a workflow JSON or PNG, ideally one that doesn't require custom nodes to test. If the bug open happens when certain custom nodes are used, most likely that custom node is what has the bug rather than ComfyUI, in which case it should be reported to the node's author."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Debug Logs
      description: 'Please copy the output from your terminal logs here.'
      render: powershell
    validations:
      required: true
  - type: textarea
    attributes:
      label: Browser Logs
      description: 'Please copy the output from your browser logs here. You can access this by pressing F12 to toggle the developer tools, then navigating to the Console tab.'
    validations:
      required: true
  - type: textarea
    attributes:
      label: Setting JSON
      description: 'Please upload the setting file here. The setting file is located at `user/default/comfy.settings.json`'
    validations:
      required: true
  - type: dropdown
    id: browsers
    attributes:
      label: What browsers do you use to access the UI ?
      multiple: true
      options:
        - Mozilla Firefox
        - Google Chrome
        - Brave
        - Apple Safari
        - Microsoft Edge
        - Android
        - iOS
        - Other
  - type: textarea
    attributes:
      label: Other Information
      description: 'Any other context, details, or screenshots that might help solve the issue.'
      placeholder: 'Add any other relevant information here...'
    validations:
      required: false
