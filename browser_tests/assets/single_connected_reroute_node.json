{"last_node_id": 16, "last_link_id": 18, "nodes": [{"id": 12, "type": "VAEDecode", "pos": [620, 260], "size": [210, 46], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": null}, {"name": "vae", "type": "VAE", "link": 18}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": null}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 13, "type": "Reroute", "pos": [510, 280], "size": [75, 26], "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 13}], "outputs": [{"name": "", "type": "VAE", "links": [18], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [160, 240], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [13], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["v1-5-pruned-em<PERSON><PERSON><PERSON>.safetensors"]}], "links": [[13, 4, 2, 13, 0, "*"], [18, 13, 0, 12, 1, "VAE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [0, 0]}}, "version": 0.4}