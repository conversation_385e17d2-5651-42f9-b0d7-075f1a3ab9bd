name: Update Comfy Registry API Types

on:
  # Manual trigger
  workflow_dispatch:

  # Triggered from comfy-api repo
  repository_dispatch:
    types: [comfy-api-updated]

jobs:
  update-registry-types:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Checkout comfy-api repository
        uses: actions/checkout@v4
        with:
          repository: Comfy-Org/comfy-api
          path: comfy-api
          token: ${{ secrets.COMFY_API_PAT }}
          clean: true

      - name: Get API commit information
        id: api-info
        run: |
          cd comfy-api
          API_COMMIT=$(git rev-parse --short HEAD)
          echo "commit=${API_COMMIT}" >> $GITHUB_OUTPUT
          cd ..

      - name: Generate API types
        run: |
          echo "Generating TypeScript types from comfy-api@${{ steps.api-info.outputs.commit }}..."
          npx openapi-typescript ./comfy-api/openapi.yml --output ./src/types/comfyRegistryTypes.ts

      - name: Validate generated types
        run: |
          if [ ! -f ./src/types/comfyRegistryTypes.ts ]; then
            echo "Error: Types file was not generated."
            exit 1
          fi

          # Check if file is not empty
          if [ ! -s ./src/types/comfyRegistryTypes.ts ]; then
            echo "Error: Generated types file is empty."
            exit 1
          fi

      - name: Check for changes
        id: check-changes
        run: |
          if [[ -z $(git status --porcelain ./src/types/comfyRegistryTypes.ts) ]]; then
            echo "No changes to Comfy Registry API types detected."
            echo "changed=false" >> $GITHUB_OUTPUT
            exit 0
          else
            echo "Changes detected in Comfy Registry API types."
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Create Pull Request
        if: steps.check-changes.outputs.changed == 'true'
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.PR_GH_TOKEN }}
          commit-message: '[chore] Update Comfy Registry API types from comfy-api@${{ steps.api-info.outputs.commit }}'
          title: '[chore] Update Comfy Registry API types from comfy-api@${{ steps.api-info.outputs.commit }}'
          body: |
            ## Automated API Type Update

            This PR updates the Comfy Registry API types from the latest comfy-api OpenAPI specification.

            - API commit: ${{ steps.api-info.outputs.commit }}
            - Generated on: ${{ github.event.repository.updated_at }}

            These types are automatically generated using openapi-typescript.
          branch: update-registry-types-${{ steps.api-info.outputs.commit }}
          base: main
          labels: CNR
          delete-branch: true
          add-paths: |
            src/types/comfyRegistryTypes.ts
