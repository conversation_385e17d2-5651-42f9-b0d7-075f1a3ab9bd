name: Update Electron Types

on:
  workflow_dispatch:

jobs:
  update-electron-types:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'npm'

      - name: Update electron types
        run: npm install @comfyorg/comfyui-electron-types@latest

      - name: Get new version
        id: get-version
        run: |
          NEW_VERSION=$(node -e "console.log(JSON.parse(require('fs').readFileSync('./package-lock.json')).packages['node_modules/@comfyorg/comfyui-electron-types'].version)")
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_OUTPUT

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.PR_GH_TOKEN }}
          commit-message: '[chore] Update electron-types to ${{ steps.get-version.outputs.NEW_VERSION }}'
          title: '[chore] Update electron-types to ${{ steps.get-version.outputs.NEW_VERSION }}'
          body: |
            Automated update of desktop API types to version ${{ steps.get-version.outputs.NEW_VERSION }}.
          branch: update-electron-types-${{ steps.get-version.outputs.NEW_VERSION }}
          base: main
          labels: |
            dependencies
            Electron
